{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\Pagination\\index.vue?vue&type=style&index=0&id=72233bcd&scoped=true&lang=css", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\Pagination\\index.vue", "mtime": 1718070340300}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758071060223}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758071061987}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758071060880}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5wYWdpbmF0aW9uLWNvbnRhaW5lciB7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIHBhZGRpbmc6IDMycHggMTZweDsNCn0NCi5wYWdpbmF0aW9uLWNvbnRhaW5lci5oaWRkZW4gew0KICBkaXNwbGF5OiBub25lOw0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6FA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Pagination", "sourcesContent": ["<template>\r\n  <div :class=\"{'hidden':hidden}\" class=\"pagination-container\">\r\n    <el-pagination\r\n      :background=\"background\"\r\n      :current-page.sync=\"currentPage\"\r\n      :page-size.sync=\"pageSize\"\r\n      :layout=\"layout\"\r\n      :page-sizes=\"pageSizes\"\r\n      :total=\"total\"\r\n      v-bind=\"$attrs\"\r\n      @size-change=\"handleSizeChange\"\r\n      @current-change=\"handleCurrentChange\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { scrollTo } from '@/utils/scroll-to'\r\n\r\nexport default {\r\n  name: 'Pagination',\r\n  props: {\r\n    total: {\r\n      required: true,\r\n      type: Number\r\n    },\r\n    page: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    limit: {\r\n      type: Number,\r\n      default: 20\r\n    },\r\n    pageSizes: {\r\n      type: Array,\r\n      default() {\r\n        return [10, 20, 30, 50]\r\n      }\r\n    },\r\n    layout: {\r\n      type: String,\r\n      default: 'total, sizes, prev, pager, next, jumper'\r\n    },\r\n    background: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    autoScroll: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    hidden: {\r\n      type: <PERSON>olean,\r\n      default: false\r\n    }\r\n  },\r\n  computed: {\r\n    currentPage: {\r\n      get() {\r\n        return this.page\r\n      },\r\n      set(val) {\r\n        this.$emit('update:page', val)\r\n      }\r\n    },\r\n    pageSize: {\r\n      get() {\r\n        return this.limit\r\n      },\r\n      set(val) {\r\n        this.$emit('update:limit', val)\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    handleSizeChange(val) {\r\n      this.$emit('pagination', { page: this.currentPage, limit: val })\r\n      if (this.autoScroll) {\r\n        scrollTo(0, 800)\r\n      }\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.$emit('pagination', { page: val, limit: this.pageSize })\r\n      if (this.autoScroll) {\r\n        scrollTo(0, 800)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.pagination-container {\r\n  background: #fff;\r\n  padding: 32px 16px;\r\n}\r\n.pagination-container.hidden {\r\n  display: none;\r\n}\r\n</style>\r\n"]}]}