{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project_about\\git174\\alznt-admin\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\project_about\\git174\\alznt-admin\\src\\api\\screen\\EarlyWarningManagement\\ywtgYjSb.js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\api\\screen\\EarlyWarningManagement\\ywtgYjSb.js", "mtime": 1718329525146}, {"path": "D:\\project_about\\git174\\alznt-admin\\babel.config.js", "mtime": 1718238659557}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1758071059938}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9wcm9qZWN0X2Fib3V0L2dpdDE3NC9hbHpudC1hZG1pbi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZXhwb3J0WXd0Z1lqU2IgPSBleHBvcnRZd3RnWWpTYjsKZXhwb3J0cy5saXN0WXd0Z1BrID0gbGlzdFl3dGdQazsKZXhwb3J0cy5saXN0WXd0Z1lqU2IgPSBsaXN0WXd0Z1lqU2I7CmV4cG9ydHMudXBkYXRlWXd0Z1lqU2IgPSB1cGRhdGVZd3RnWWpTYjsKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Ci8vIOafpeivoumihOitpuiuvuWkh+WIl+ihqApmdW5jdGlvbiBsaXN0WXd0Z1lqU2IocXVlcnkpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogIi9rZnF5d3RnL3l3dGdZalNiL2xpc3QiLAogICAgbWV0aG9kOiAiZ2V0IiwKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5p+l6K+i54mp6IGU5bmz5Y+w5Lqn5ZOB5YiX5YiX6KGoCmZ1bmN0aW9uIGxpc3RZd3RnUGsoKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICIva2ZxeXd0Zy9way9saXN0IiwKICAgIG1ldGhvZDogImdldCIKICB9KTsKfQoKLy8g5L+u5pS56aKE6K2m6K6+5aSHCmZ1bmN0aW9uIHVwZGF0ZVl3dGdZalNiKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogIi9rZnF5d3RnL3l3dGdZalNiL2VkaXQiLAogICAgbWV0aG9kOiAicG9zdCIsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOWvvOWHuumihOitpuiuvuWkhwpmdW5jdGlvbiBleHBvcnRZd3RnWWpTYihxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAiL2tmcXl3dGcveXd0Z1lqU2IvZXhwb3J0IiwKICAgIG1ldGhvZDogImdldCIsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listYwtgYjSb", "query", "request", "url", "method", "params", "listYwtgPk", "updateYwtgYjSb", "data", "exportYwtgYjSb"], "sources": ["D:/project_about/git174/alznt-admin/src/api/screen/EarlyWarningManagement/ywtgYjSb.js"], "sourcesContent": ["import request from \"@/utils/request\";\r\n\r\n// 查询预警设备列表\r\nexport function listYwtgYjSb(query) {\r\n  return request({\r\n    url: \"/kfqywtg/ywtgYjSb/list\",\r\n    method: \"get\",\r\n    params: query,\r\n  });\r\n}\r\n\r\n// 查询物联平台产品列列表\r\nexport function listYwtgPk() {\r\n  return request({\r\n    url: \"/kfqywtg/pk/list\",\r\n    method: \"get\",\r\n  });\r\n}\r\n\r\n// 修改预警设备\r\nexport function updateYwtgYjSb(data) {\r\n  return request({\r\n    url: \"/kfqywtg/ywtgYjSb/edit\",\r\n    method: \"post\",\r\n    data: data,\r\n  });\r\n}\r\n\r\n// 导出预警设备\r\nexport function exportYwtgYjSb(query) {\r\n  return request({\r\n    url: \"/kfqywtg/ywtgYjSb/export\",\r\n    method: \"get\",\r\n    params: query,\r\n  });\r\n}\r\n"], "mappings": ";;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,YAAYA,CAACC,KAAK,EAAE;EAClC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,UAAUA,CAAA,EAAG;EAC3B,OAAO,IAAAJ,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,cAAcA,CAACC,IAAI,EAAE;EACnC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,MAAM;IACdI,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,cAAcA,CAACR,KAAK,EAAE;EACpC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}