{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\ImageUpload\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\ImageUpload\\index.vue", "mtime": 1718070340300}, {"path": "D:\\project_about\\git174\\alznt-admin\\babel.config.js", "mtime": 1718238659557}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfYXV0aCA9IHJlcXVpcmUoIkAvdXRpbHMvYXV0aCIpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICB1cGxvYWRJbWdVcmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL2NvbW1vbi91cGxvYWQiLAogICAgICAvLyDkuIrkvKDnmoTlm77niYfmnI3liqHlmajlnLDlnYAKICAgICAgaGVhZGVyczogewogICAgICAgIEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArICgwLCBfYXV0aC5nZXRUb2tlbikoKQogICAgICB9CiAgICB9OwogIH0sCiAgcHJvcHM6IHsKICAgIHZhbHVlOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogIiIKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIHJlbW92ZUltYWdlOiBmdW5jdGlvbiByZW1vdmVJbWFnZSgpIHsKICAgICAgdGhpcy4kZW1pdCgiaW5wdXQiLCAiIik7CiAgICB9LAogICAgaGFuZGxlVXBsb2FkU3VjY2VzczogZnVuY3Rpb24gaGFuZGxlVXBsb2FkU3VjY2VzcyhyZXMpIHsKICAgICAgdGhpcy4kZW1pdCgiaW5wdXQiLCByZXMudXJsKTsKICAgICAgdGhpcy5sb2FkaW5nLmNsb3NlKCk7CiAgICB9LAogICAgaGFuZGxlQmVmb3JlVXBsb2FkOiBmdW5jdGlvbiBoYW5kbGVCZWZvcmVVcGxvYWQoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRoaXMuJGxvYWRpbmcoewogICAgICAgIGxvY2s6IHRydWUsCiAgICAgICAgdGV4dDogIuS4iuS8oOS4rSIsCiAgICAgICAgYmFja2dyb3VuZDogInJnYmEoMCwgMCwgMCwgMC43KSIKICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlVXBsb2FkRXJyb3I6IGZ1bmN0aW9uIGhhbmRsZVVwbG9hZEVycm9yKCkgewogICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICB0eXBlOiAiZXJyb3IiLAogICAgICAgIG1lc3NhZ2U6ICLkuIrkvKDlpLHotKUiCiAgICAgIH0pOwogICAgICB0aGlzLmxvYWRpbmcuY2xvc2UoKTsKICAgIH0KICB9LAogIHdhdGNoOiB7fQp9Ow=="}, {"version": 3, "names": ["_auth", "require", "data", "dialogVisible", "uploadImgUrl", "process", "env", "VUE_APP_BASE_API", "headers", "Authorization", "getToken", "props", "value", "type", "String", "default", "methods", "removeImage", "$emit", "handleUploadSuccess", "res", "url", "loading", "close", "handleBeforeUpload", "$loading", "lock", "text", "background", "handleUploadError", "$message", "message", "watch"], "sources": ["src/components/ImageUpload/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"component-upload-image\">\r\n    <el-upload\r\n      :action=\"uploadImgUrl\"\r\n      list-type=\"picture-card\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n      :on-error=\"handleUploadError\"\r\n      name=\"file\"\r\n      :show-file-list=\"false\"\r\n      :headers=\"headers\"\r\n      style=\"display: inline-block; vertical-align: top\"\r\n    >\r\n      <el-image v-if=\"!value\" :src=\"value\">\r\n        <div slot=\"error\" class=\"image-slot\">\r\n          <i class=\"el-icon-plus\" />\r\n        </div>\r\n      </el-image>\r\n      <div v-else class=\"image\">\r\n        <el-image :src=\"value\" :style=\"`width:150px;height:150px;`\" fit=\"fill\"/>\r\n        <div class=\"mask\">\r\n          <div class=\"actions\">\r\n            <span title=\"预览\" @click.stop=\"dialogVisible = true\">\r\n              <i class=\"el-icon-zoom-in\" />\r\n            </span>\r\n            <span title=\"移除\" @click.stop=\"removeImage\">\r\n              <i class=\"el-icon-delete\" />\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-upload>\r\n    <el-dialog :visible.sync=\"dialogVisible\" title=\"预览\" width=\"800\" append-to-body>\r\n      <img :src=\"value\" style=\"display: block; max-width: 100%; margin: 0 auto;\">\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      uploadImgUrl: process.env.VUE_APP_BASE_API + \"/common/upload\", // 上传的图片服务器地址\r\n      headers: {\r\n        Authorization: \"Bearer \" + getToken(),\r\n      },\r\n    };\r\n  },\r\n  props: {\r\n    value: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n  },\r\n  methods: {\r\n    removeImage() {\r\n      this.$emit(\"input\", \"\");\r\n    },\r\n    handleUploadSuccess(res) {\r\n      this.$emit(\"input\", res.url);\r\n      this.loading.close();\r\n    },\r\n    handleBeforeUpload() {\r\n      this.loading = this.$loading({\r\n        lock: true,\r\n        text: \"上传中\",\r\n        background: \"rgba(0, 0, 0, 0.7)\",\r\n      });\r\n    },\r\n    handleUploadError() {\r\n      this.$message({\r\n        type: \"error\",\r\n        message: \"上传失败\",\r\n      });\r\n      this.loading.close();\r\n    },\r\n  },\r\n  watch: {},\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.image {\r\n  position: relative;\r\n  .mask {\r\n    opacity: 0;\r\n    position: absolute;\r\n    top: 0;\r\n    width: 100%;\r\n    background-color: rgba(0, 0, 0, 0.5);\r\n    transition: all 0.3s;\r\n  }\r\n  &:hover .mask {\r\n    opacity: 1;\r\n  }\r\n}\r\n</style>"], "mappings": ";;;;;;AAuCA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,YAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MAAA;MACAC,OAAA;QACAC,aAAA,kBAAAC,cAAA;MACA;IACA;EACA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAC,KAAA;IACA;IACAC,mBAAA,WAAAA,oBAAAC,GAAA;MACA,KAAAF,KAAA,UAAAE,GAAA,CAAAC,GAAA;MACA,KAAAC,OAAA,CAAAC,KAAA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MACA,KAAAF,OAAA,QAAAG,QAAA;QACAC,IAAA;QACAC,IAAA;QACAC,UAAA;MACA;IACA;IACAC,iBAAA,WAAAA,kBAAA;MACA,KAAAC,QAAA;QACAjB,IAAA;QACAkB,OAAA;MACA;MACA,KAAAT,OAAA,CAAAC,KAAA;IACA;EACA;EACAS,KAAA;AACA", "ignoreList": []}]}