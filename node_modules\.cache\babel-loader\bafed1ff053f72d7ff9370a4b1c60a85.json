{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\Breadcrumb\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\Breadcrumb\\index.vue", "mtime": 1718070340298}, {"path": "D:\\project_about\\git174\\alznt-admin\\babel.config.js", "mtime": 1718238659557}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZpbHRlci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuZnVuY3Rpb24ubmFtZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnN0YXJ0cy13aXRoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcudHJpbS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuZmlsdGVyLmpzIik7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbGV2ZWxMaXN0OiBudWxsCiAgICB9OwogIH0sCiAgd2F0Y2g6IHsKICAgICRyb3V0ZTogZnVuY3Rpb24gJHJvdXRlKHJvdXRlKSB7CiAgICAgIC8vIGlmIHlvdSBnbyB0byB0aGUgcmVkaXJlY3QgcGFnZSwgZG8gbm90IHVwZGF0ZSB0aGUgYnJlYWRjcnVtYnMKICAgICAgaWYgKHJvdXRlLnBhdGguc3RhcnRzV2l0aCgnL3JlZGlyZWN0LycpKSB7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuZ2V0QnJlYWRjcnVtYigpOwogICAgfQogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0QnJlYWRjcnVtYigpOwogIH0sCiAgbWV0aG9kczogewogICAgZ2V0QnJlYWRjcnVtYjogZnVuY3Rpb24gZ2V0QnJlYWRjcnVtYigpIHsKICAgICAgLy8gb25seSBzaG93IHJvdXRlcyB3aXRoIG1ldGEudGl0bGUKICAgICAgdmFyIG1hdGNoZWQgPSB0aGlzLiRyb3V0ZS5tYXRjaGVkLmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLm1ldGEgJiYgaXRlbS5tZXRhLnRpdGxlOwogICAgICB9KTsKICAgICAgdmFyIGZpcnN0ID0gbWF0Y2hlZFswXTsKICAgICAgaWYgKCF0aGlzLmlzRGFzaGJvYXJkKGZpcnN0KSkgewogICAgICAgIG1hdGNoZWQgPSBbewogICAgICAgICAgcGF0aDogJy9pbmRleCcsCiAgICAgICAgICBtZXRhOiB7CiAgICAgICAgICAgIHRpdGxlOiAn6aaW6aG1JwogICAgICAgICAgfQogICAgICAgIH1dLmNvbmNhdChtYXRjaGVkKTsKICAgICAgfQogICAgICB0aGlzLmxldmVsTGlzdCA9IG1hdGNoZWQuZmlsdGVyKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0ubWV0YSAmJiBpdGVtLm1ldGEudGl0bGUgJiYgaXRlbS5tZXRhLmJyZWFkY3J1bWIgIT09IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICBpc0Rhc2hib2FyZDogZnVuY3Rpb24gaXNEYXNoYm9hcmQocm91dGUpIHsKICAgICAgdmFyIG5hbWUgPSByb3V0ZSAmJiByb3V0ZS5uYW1lOwogICAgICBpZiAoIW5hbWUpIHsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgcmV0dXJuIG5hbWUudHJpbSgpID09PSAn6aaW6aG1JzsKICAgIH0sCiAgICBoYW5kbGVMaW5rOiBmdW5jdGlvbiBoYW5kbGVMaW5rKGl0ZW0pIHsKICAgICAgdmFyIHJlZGlyZWN0ID0gaXRlbS5yZWRpcmVjdCwKICAgICAgICBwYXRoID0gaXRlbS5wYXRoOwogICAgICBpZiAocmVkaXJlY3QpIHsKICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaChyZWRpcmVjdCk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHBhdGgpOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["data", "levelList", "watch", "$route", "route", "path", "startsWith", "getBreadcrumb", "created", "methods", "matched", "filter", "item", "meta", "title", "first", "isDashboard", "concat", "breadcrumb", "name", "trim", "handleLink", "redirect", "$router", "push"], "sources": ["src/components/Breadcrumb/index.vue"], "sourcesContent": ["<template>\r\n  <el-breadcrumb class=\"app-breadcrumb\" separator=\"/\">\r\n    <transition-group name=\"breadcrumb\">\r\n      <el-breadcrumb-item v-for=\"(item,index) in levelList\" :key=\"item.path\">\r\n        <span v-if=\"item.redirect==='noRedirect'||index==levelList.length-1\" class=\"no-redirect\">{{ item.meta.title }}</span>\r\n        <a v-else @click.prevent=\"handleLink(item)\">{{ item.meta.title }}</a>\r\n      </el-breadcrumb-item>\r\n    </transition-group>\r\n  </el-breadcrumb>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      levelList: null\r\n    }\r\n  },\r\n  watch: {\r\n    $route(route) {\r\n      // if you go to the redirect page, do not update the breadcrumbs\r\n      if (route.path.startsWith('/redirect/')) {\r\n        return\r\n      }\r\n      this.getBreadcrumb()\r\n    }\r\n  },\r\n  created() {\r\n    this.getBreadcrumb()\r\n  },\r\n  methods: {\r\n    getBreadcrumb() {\r\n      // only show routes with meta.title\r\n      let matched = this.$route.matched.filter(item => item.meta && item.meta.title)\r\n      const first = matched[0]\r\n\r\n      if (!this.isDashboard(first)) {\r\n        matched = [{ path: '/index', meta: { title: '首页' }}].concat(matched)\r\n      }\r\n\r\n      this.levelList = matched.filter(item => item.meta && item.meta.title && item.meta.breadcrumb !== false)\r\n    },\r\n    isDashboard(route) {\r\n      const name = route && route.name\r\n      if (!name) {\r\n        return false\r\n      }\r\n      return name.trim() === '首页'\r\n    },\r\n    handleLink(item) {\r\n      const { redirect, path } = item\r\n      if (redirect) {\r\n        this.$router.push(redirect)\r\n        return\r\n      }\r\n      this.$router.push(path)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-breadcrumb.el-breadcrumb {\r\n  display: inline-block;\r\n  font-size: 14px;\r\n  line-height: 50px;\r\n  margin-left: 8px;\r\n\r\n  .no-redirect {\r\n    color: #97a8be;\r\n    cursor: text;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;iCAYA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;IACA;EACA;EACAC,KAAA;IACAC,MAAA,WAAAA,OAAAC,KAAA;MACA;MACA,IAAAA,KAAA,CAAAC,IAAA,CAAAC,UAAA;QACA;MACA;MACA,KAAAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAD,aAAA;EACA;EACAE,OAAA;IACAF,aAAA,WAAAA,cAAA;MACA;MACA,IAAAG,OAAA,QAAAP,MAAA,CAAAO,OAAA,CAAAC,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,IAAA,IAAAD,IAAA,CAAAC,IAAA,CAAAC,KAAA;MAAA;MACA,IAAAC,KAAA,GAAAL,OAAA;MAEA,UAAAM,WAAA,CAAAD,KAAA;QACAL,OAAA;UAAAL,IAAA;UAAAQ,IAAA;YAAAC,KAAA;UAAA;QAAA,GAAAG,MAAA,CAAAP,OAAA;MACA;MAEA,KAAAT,SAAA,GAAAS,OAAA,CAAAC,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,IAAA,IAAAD,IAAA,CAAAC,IAAA,CAAAC,KAAA,IAAAF,IAAA,CAAAC,IAAA,CAAAK,UAAA;MAAA;IACA;IACAF,WAAA,WAAAA,YAAAZ,KAAA;MACA,IAAAe,IAAA,GAAAf,KAAA,IAAAA,KAAA,CAAAe,IAAA;MACA,KAAAA,IAAA;QACA;MACA;MACA,OAAAA,IAAA,CAAAC,IAAA;IACA;IACAC,UAAA,WAAAA,WAAAT,IAAA;MACA,IAAAU,QAAA,GAAAV,IAAA,CAAAU,QAAA;QAAAjB,IAAA,GAAAO,IAAA,CAAAP,IAAA;MACA,IAAAiB,QAAA;QACA,KAAAC,OAAA,CAAAC,IAAA,CAAAF,QAAA;QACA;MACA;MACA,KAAAC,OAAA,CAAAC,IAAA,CAAAnB,IAAA;IACA;EACA;AACA", "ignoreList": []}]}