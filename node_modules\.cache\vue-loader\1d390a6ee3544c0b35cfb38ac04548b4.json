{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\SelectTree\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\SelectTree\\index.vue", "mtime": 1740448089611}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAqBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/SelectTree", "sourcesContent": ["<!-- el-select & el-tree 下拉树形选择 -->\r\n<template>\r\n  <div>\r\n    <el-select ref=\"selectTree\" v-model=\"localValue\" :multiple=\"multiple\" :clearable=\"clearable\" @clear=\"clearHandle\" :disabled=\"disabled\">\r\n      <el-option v-for=\"option in options\" :key=\"option[props.value]\" :value=\"option[props.value]\">\r\n        <el-tree\r\n          :data=\"options\"\r\n          :props=\"props\"\r\n          :node-key=\"props.value\"\r\n          @node-click=\"handleNodeClick\"\r\n          :accordion=\"accordion\"\r\n        >\r\n          <span slot-scope=\"{ data }\">\r\n            <i :class=\"[data.color != null ? 'ification_col' : '']\" :style=\"{'background-color': data.color}\"></i>&nbsp;&nbsp;{{ data.label }}\r\n          </span>\r\n        </el-tree>\r\n      </el-option>\r\n    </el-select>\r\n  </div>\r\n</template>\r\n\r\n<script>export default {\r\n  name: \"el-tree-select\",\r\n  props: {\r\n    // 配置项\r\n    props: {\r\n      type: Object,\r\n      default: () => ({\r\n        value: 'id',\r\n        children: 'children',\r\n        label: 'name'\r\n      })\r\n    },\r\n    // 是否禁用\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 选项列表数据(树形结构的对象数组)\r\n    options: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 初始值（单选或多选）\r\n    value: {\r\n      type: [String, Number, Object, Array], // 允许多种类型\r\n      default: null\r\n    },\r\n    // 可清空选项\r\n    clearable: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    // 自动收起\r\n    accordion: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 是否多选\r\n    multiple: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      localValue: this.value || (this.multiple ? [] : null), // 使用 value 或者默认值\r\n    }\r\n  },\r\n  watch: {\r\n    value: {\r\n      immediate: true,\r\n      handler(newValue) {\r\n        this.localValue = newValue;\r\n      }\r\n    },\r\n    localValue: {\r\n      handler(newValue) {\r\n        this.$emit('input', newValue);\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    // 清除选中\r\n    clearHandle() {\r\n      this.localValue = this.multiple ? [] : null;\r\n      this.clearSelected();\r\n      this.$emit('input', this.localValue);\r\n    },\r\n    // 切换选项\r\n    handleNodeClick(node) {\r\n      if (this.multiple) {\r\n        // 多选（判重后添加）\r\n        if (!this.localValue.includes(node[this.props.label])) {\r\n          this.localValue.push(node[this.props.label]);\r\n        }\r\n      } else {\r\n        // 单选\r\n        this.localValue = node[this.props.label];\r\n      }\r\n      this.$emit('input', this.localValue);\r\n    },\r\n    // 清空选中样式\r\n    clearSelected() {\r\n      const treeNodes = this.$refs.selectTree.$el.querySelectorAll('.el-tree-node');\r\n      treeNodes.forEach(node => node.classList.remove('is-current'));\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style scoped>\r\n.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {\r\n  height: auto;\r\n  max-height: 300px;\r\n  padding: 0;\r\n  overflow: hidden;\r\n  overflow-y: auto;\r\n}\r\n.el-select-dropdown__item.selected {\r\n  font-weight: normal;\r\n}\r\n.el-tree .el-tree-node__content {\r\n  height: auto;\r\n  padding: 0 20px;\r\n}\r\n.el-tree-node__label {\r\n  font-weight: normal;\r\n}\r\n.el-tree >>> .is-current .el-tree-node__label {\r\n  color: #409EFF;\r\n  font-weight: 700;\r\n}\r\n.el-tree >>> .is-current .el-tree-node__children .el-tree-node__label {\r\n  color: #606266;\r\n  font-weight: normal;\r\n}\r\n.el-popper {\r\n  z-index: 9999;\r\n}\r\n.ification_col {\r\n  width: 20px;\r\n  height: 10px;\r\n  display: inline-block;\r\n}\r\n</style>\r\n"]}]}