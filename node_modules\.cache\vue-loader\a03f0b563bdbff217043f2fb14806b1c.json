{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\Pagination\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\Pagination\\index.vue", "mtime": 1718070340300}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBzY3JvbGxUbyB9IGZyb20gJ0AvdXRpbHMvc2Nyb2xsLXRvJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdQYWdpbmF0aW9uJywNCiAgcHJvcHM6IHsNCiAgICB0b3RhbDogew0KICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICB0eXBlOiBOdW1iZXINCiAgICB9LA0KICAgIHBhZ2U6IHsNCiAgICAgIHR5cGU6IE51bWJlciwNCiAgICAgIGRlZmF1bHQ6IDENCiAgICB9LA0KICAgIGxpbWl0OiB7DQogICAgICB0eXBlOiBOdW1iZXIsDQogICAgICBkZWZhdWx0OiAyMA0KICAgIH0sDQogICAgcGFnZVNpemVzOiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQoKSB7DQogICAgICAgIHJldHVybiBbMTAsIDIwLCAzMCwgNTBdDQogICAgICB9DQogICAgfSwNCiAgICBsYXlvdXQ6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICd0b3RhbCwgc2l6ZXMsIHByZXYsIHBhZ2VyLCBuZXh0LCBqdW1wZXInDQogICAgfSwNCiAgICBiYWNrZ3JvdW5kOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogdHJ1ZQ0KICAgIH0sDQogICAgYXV0b1Njcm9sbDogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IHRydWUNCiAgICB9LA0KICAgIGhpZGRlbjogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIGN1cnJlbnRQYWdlOiB7DQogICAgICBnZXQoKSB7DQogICAgICAgIHJldHVybiB0aGlzLnBhZ2UNCiAgICAgIH0sDQogICAgICBzZXQodmFsKSB7DQogICAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTpwYWdlJywgdmFsKQ0KICAgICAgfQ0KICAgIH0sDQogICAgcGFnZVNpemU6IHsNCiAgICAgIGdldCgpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMubGltaXQNCiAgICAgIH0sDQogICAgICBzZXQodmFsKSB7DQogICAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTpsaW1pdCcsIHZhbCkNCiAgICAgIH0NCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBoYW5kbGVTaXplQ2hhbmdlKHZhbCkgew0KICAgICAgdGhpcy4kZW1pdCgncGFnaW5hdGlvbicsIHsgcGFnZTogdGhpcy5jdXJyZW50UGFnZSwgbGltaXQ6IHZhbCB9KQ0KICAgICAgaWYgKHRoaXMuYXV0b1Njcm9sbCkgew0KICAgICAgICBzY3JvbGxUbygwLCA4MDApDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKHZhbCkgew0KICAgICAgdGhpcy4kZW1pdCgncGFnaW5hdGlvbicsIHsgcGFnZTogdmFsLCBsaW1pdDogdGhpcy5wYWdlU2l6ZSB9KQ0KICAgICAgaWYgKHRoaXMuYXV0b1Njcm9sbCkgew0KICAgICAgICBzY3JvbGxUbygwLCA4MDApDQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAiBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Pagination", "sourcesContent": ["<template>\r\n  <div :class=\"{'hidden':hidden}\" class=\"pagination-container\">\r\n    <el-pagination\r\n      :background=\"background\"\r\n      :current-page.sync=\"currentPage\"\r\n      :page-size.sync=\"pageSize\"\r\n      :layout=\"layout\"\r\n      :page-sizes=\"pageSizes\"\r\n      :total=\"total\"\r\n      v-bind=\"$attrs\"\r\n      @size-change=\"handleSizeChange\"\r\n      @current-change=\"handleCurrentChange\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { scrollTo } from '@/utils/scroll-to'\r\n\r\nexport default {\r\n  name: 'Pagination',\r\n  props: {\r\n    total: {\r\n      required: true,\r\n      type: Number\r\n    },\r\n    page: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    limit: {\r\n      type: Number,\r\n      default: 20\r\n    },\r\n    pageSizes: {\r\n      type: Array,\r\n      default() {\r\n        return [10, 20, 30, 50]\r\n      }\r\n    },\r\n    layout: {\r\n      type: String,\r\n      default: 'total, sizes, prev, pager, next, jumper'\r\n    },\r\n    background: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    autoScroll: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    hidden: {\r\n      type: <PERSON>olean,\r\n      default: false\r\n    }\r\n  },\r\n  computed: {\r\n    currentPage: {\r\n      get() {\r\n        return this.page\r\n      },\r\n      set(val) {\r\n        this.$emit('update:page', val)\r\n      }\r\n    },\r\n    pageSize: {\r\n      get() {\r\n        return this.limit\r\n      },\r\n      set(val) {\r\n        this.$emit('update:limit', val)\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    handleSizeChange(val) {\r\n      this.$emit('pagination', { page: this.currentPage, limit: val })\r\n      if (this.autoScroll) {\r\n        scrollTo(0, 800)\r\n      }\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.$emit('pagination', { page: val, limit: this.pageSize })\r\n      if (this.autoScroll) {\r\n        scrollTo(0, 800)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.pagination-container {\r\n  background: #fff;\r\n  padding: 32px 16px;\r\n}\r\n.pagination-container.hidden {\r\n  display: none;\r\n}\r\n</style>\r\n"]}]}