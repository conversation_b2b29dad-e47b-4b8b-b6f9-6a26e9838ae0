{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\easyCron\\tabs\\week.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\easyCron\\tabs\\week.vue", "mtime": 1718070340308}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgbWl4aW4gZnJvbSAnLi9taXhpbicNCmltcG9ydCB7IFdFRUtfTUFQX0VOLCByZXBsYWNlV2Vla05hbWUgfSBmcm9tICcuL2NvbnN0LmpzJw0KDQpjb25zdCBXRUVLX01BUCA9IHsNCiAgJ+WRqOaXpSc6IDAsDQogICflkajkuIAnOiAxLA0KICAn5ZGo5LqMJzogMiwNCiAgJ+WRqOS4iSc6IDMsDQogICflkajlm5snOiA0LA0KICAn5ZGo5LqUJzogNSwNCiAgJ+WRqOWFrSc6IDYNCn0NCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnd2VlaycsDQogIG1peGluczogW21peGluXSwNCiAgcHJvcHM6IHsNCiAgICBkYXk6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICcqJw0KICAgIH0NCiAgfSwNCiAgZGF0YSAoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIFdFRUtfTUFQLA0KICAgICAgV0VFS19NQVBfRU4NCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgZGlzYWJsZUNob2ljZSAoKSB7DQogICAgICByZXR1cm4gKHRoaXMuZGF5ICYmIHRoaXMuZGF5ICE9PSAnPycpIHx8IHRoaXMuZGlzYWJsZWQNCiAgICB9DQogIH0sDQogIHdhdGNoOiB7DQogICAgdmFsdWVfYyAobmV3VmFsLCBvbGRWYWwpIHsNCiAgICAgIC8vIOWmguaenOiuvue9ruaXpe+8jOmCo+S5iOaYn+acn+WwseebtOaOpeS4jeiuvue9rg0KICAgICAgdGhpcy51cGRhdGVWYWx1ZSgpDQogICAgfSwNCiAgICBkYXkgKG5ld1ZhbCkgew0KICAgICAgLy8gY29uc29sZS5pbmZvKCduZXcgZGF5OiAnICsgbmV3VmFsKQ0KICAgICAgdGhpcy51cGRhdGVWYWx1ZSgpDQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgdXBkYXRlVmFsdWUgKCkgew0KICAgICAgdGhpcy4kZW1pdCgnY2hhbmdlJywgdGhpcy5kaXNhYmxlQ2hvaWNlID8gJz8nIDogdGhpcy52YWx1ZV9jKQ0KICAgIH0sDQogICAgcHJlUHJvY2Vzc1Byb3AgKGMpIHsNCiAgICAgIHJldHVybiByZXBsYWNlV2Vla05hbWUoYykNCiAgICB9DQogIH0sDQogIGNyZWF0ZWQgKCkgew0KICAgIHRoaXMuREVGQVVMVF9WQUxVRSA9ICcqJw0KICAgIC8vIDAsN+ihqOekuuWRqOaXpSAx6KGo56S65ZGo5LiADQogICAgdGhpcy5taW5WYWx1ZSA9IDANCiAgICB0aGlzLm1heFZhbHVlID0gNg0KICAgIHRoaXMudmFsdWVSYW5nZS5zdGFydCA9IDANCiAgICB0aGlzLnZhbHVlUmFuZ2UuZW5kID0gNg0KICAgIHRoaXMudmFsdWVMb29wLnN0YXJ0ID0gMg0KICAgIHRoaXMudmFsdWVMb29wLmludGVydmFsID0gMQ0KICAgIHRoaXMucGFyc2VQcm9wKHRoaXMucHJvcCkNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["week.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "week.vue", "sourceRoot": "src/components/easyCron/tabs", "sourcesContent": ["<template>\r\n  <div class=\"config-list\">\r\n    <RadioGroup v-model=\"type\">\r\n    <div class=\"item\">\r\n      <Radio label=\"TYPE_NOT_SET\" class=\"choice\" :disabled=\"disableChoice\">不设置</Radio>\r\n      <span class=\"tip-info\">日和周只能设置其中之一</span>\r\n    </div>\r\n    <div class=\"item\">\r\n      <Radio label=\"TYPE_RANGE\" class=\"choice\" :disabled=\"disableChoice\">区间</Radio>\r\n       从<Select v-model=\"valueRange.start\"  class=\"w80\" :disabled=\"type!=TYPE_RANGE || disableChoice\">\r\n        <Option v-for=\"(v, k) of WEEK_MAP\" :value=\"v\" :key=\"`week-pre-Lf13-${v}`\">{{ k }}</Option>\r\n      </Select>\r\n       至<Select v-model=\"valueRange.end\"  class=\"w80\" :disabled=\"type!=TYPE_RANGE || disableChoice\">\r\n        <Option v-for=\"(v, k) of WEEK_MAP\" :value=\"v\" :key=\"`week-next-1fas-${v}`\">{{ k }}</Option>\r\n      </Select>\r\n    </div>\r\n    <div class=\"item\">\r\n      <Radio label=\"TYPE_LOOP\" class=\"choice\" :disabled=\"disableChoice\">循环</Radio>\r\n      从<Select v-model=\"valueLoop.start\"  class=\"w80\" :disabled=\"type!=TYPE_LOOP || disableChoice\">\r\n        <Option v-for=\"(v, k) of WEEK_MAP\" :value=\"v\" :key=\"`week-pre-Lf13-${v}`\">{{ k }}</Option>\r\n      </Select>开始，间隔\r\n      <InputNumber :disabled=\"type!=TYPE_LOOP || disableChoice\" :max=\"maxValue\" :min=\"minValue\" :precision=\"0\"\r\n       class=\"w60\" v-model=\"valueLoop.interval\" /> 天\r\n    </div>\r\n    <div class=\"item\">\r\n      <Radio  label=\"TYPE_SPECIFY\" class=\"choice\" :disabled=\"disableChoice\">指定</Radio>\r\n      <div class=\"list\">\r\n        <CheckboxGroup v-model=\"valueList\">\r\n          <Checkbox class=\"list-check-item\" v-for=\"(v, k) of WEEK_MAP\"\r\n            :label=\"v\" :key=\"`key-01jfs-${v}`\" :disabled=\"type!=TYPE_SPECIFY || disableChoice\"><span>{{k}}</span></Checkbox>\r\n        </CheckboxGroup>\r\n      </div>\r\n    </div>\r\n    </RadioGroup>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport mixin from './mixin'\r\nimport { WEEK_MAP_EN, replaceWeekName } from './const.js'\r\n\r\nconst WEEK_MAP = {\r\n  '周日': 0,\r\n  '周一': 1,\r\n  '周二': 2,\r\n  '周三': 3,\r\n  '周四': 4,\r\n  '周五': 5,\r\n  '周六': 6\r\n}\r\n\r\nexport default {\r\n  name: 'week',\r\n  mixins: [mixin],\r\n  props: {\r\n    day: {\r\n      type: String,\r\n      default: '*'\r\n    }\r\n  },\r\n  data () {\r\n    return {\r\n      WEEK_MAP,\r\n      WEEK_MAP_EN\r\n    }\r\n  },\r\n  computed: {\r\n    disableChoice () {\r\n      return (this.day && this.day !== '?') || this.disabled\r\n    }\r\n  },\r\n  watch: {\r\n    value_c (newVal, oldVal) {\r\n      // 如果设置日，那么星期就直接不设置\r\n      this.updateValue()\r\n    },\r\n    day (newVal) {\r\n      // console.info('new day: ' + newVal)\r\n      this.updateValue()\r\n    }\r\n  },\r\n  methods: {\r\n    updateValue () {\r\n      this.$emit('change', this.disableChoice ? '?' : this.value_c)\r\n    },\r\n    preProcessProp (c) {\r\n      return replaceWeekName(c)\r\n    }\r\n  },\r\n  created () {\r\n    this.DEFAULT_VALUE = '*'\r\n    // 0,7表示周日 1表示周一\r\n    this.minValue = 0\r\n    this.maxValue = 6\r\n    this.valueRange.start = 0\r\n    this.valueRange.end = 6\r\n    this.valueLoop.start = 2\r\n    this.valueLoop.interval = 1\r\n    this.parseProp(this.prop)\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n.config-list {\r\n  text-align: left;\r\n  margin: 0 10px 10px 10px;\r\n}\r\n\r\n.item {\r\n  margin-top: 5px;\r\n}\r\n\r\n.tip-info {\r\n  color: #999\r\n}\r\n\r\n.choice {\r\n  border: 1px solid transparent;\r\n  padding: 5px 8px;\r\n}\r\n\r\n.choice:hover {\r\n  border: 1px solid #1890ff;\r\n}\r\n\r\n.w80 {\r\n  width: 80px;\r\n}\r\n\r\n.ivu-input-number, .ivu-select {\r\n  margin-left: 5px;\r\n  margin-right: 5px;\r\n}\r\n\r\n.list {\r\n  margin: 0 20px;\r\n}\r\n\r\n.list-check-item {\r\n  padding: 1px 3px;\r\n  width: 4em;\r\n}\r\n</style>\r\n"]}]}