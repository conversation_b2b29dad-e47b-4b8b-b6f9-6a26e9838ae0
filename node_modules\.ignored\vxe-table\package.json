{"name": "vxe-table", "version": "3.18.12", "description": "A PC-end table component based on Vxe UI, supporting copy-paste, data pivot table, and high-performance virtual list table solution.", "scripts": {"update": "npm install --legacy-peer-deps", "serve": "vue-cli-service serve", "lint": "vue-cli-service lint", "build": "vue-cli-service build", "lib:modules": "gulp build", "lib:pack": "vue-cli-service build --target lib --name index --dest lib_temp index.ts", "lib": "npm run lib:pack && npm run lib:modules", "format": "eslint --fix examples/**/*.{js,ts,vue}"}, "files": ["lib", "es", "src", "helper", "types", "styles", "packages"], "main": "lib/index.common.js", "module": "es/index.esm.js", "unpkg": "lib/index.umd.js", "jsdelivr": "lib/index.umd.js", "style": "lib/style.css", "typings": "types/index.d.ts", "dependencies": {"vxe-pc-ui": "^3.8.0"}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.25.7", "@types/resize-observer-browser": "^0.1.11", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-standard": "^6.1.0", "@vue/eslint-config-typescript": "^9.1.0", "core-js": "^3.8.3", "del": "^6.1.1", "eslint": "^7.32.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^8.0.3", "gulp": "^4.0.2", "gulp-autoprefixer": "^8.0.0", "gulp-babel": "^8.0.0", "gulp-clean-css": "^4.3.0", "gulp-concat": "^2.6.1", "gulp-rename": "^2.0.0", "gulp-replace": "^1.1.4", "gulp-sass": "^5.1.0", "gulp-sourcemaps": "^3.0.0", "gulp-typescript": "^5.0.1", "gulp-uglify": "^3.0.2", "sass": "^1.80.4", "sass-loader": "^14.2.1", "typescript": "~4.5.5", "vue": "~2.6.14", "vue-i18n": "^8.15.1", "vue-router": "^3.5.1", "vue-template-compiler": "2.6.14", "vxe-gantt": "~3.0.12"}, "vetur": {"tags": "helper/vetur/tags.json", "attributes": "helper/vetur/attributes.json"}, "repository": {"type": "git", "url": "git+https://github.com/x-extends/vxe-table.git"}, "keywords": ["vxe", "vxe-ui", "vxe-table"], "author": {"name": "<PERSON>", "email": "xu_liang<PERSON><PERSON>@163.com"}, "license": "MIT", "bugs": {"url": "https://github.com/x-extends/vxe-table/issues"}}