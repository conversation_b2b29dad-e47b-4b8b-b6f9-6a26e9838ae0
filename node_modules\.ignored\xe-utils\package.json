{"name": "xe-utils", "version": "3.7.9", "description": "JavaScript 函数库、工具类", "main": "index.js", "unpkg": "dist/xe-utils.umd.min.js", "jsdelivr": "dist/xe-utils.umd.min.js", "typings": "index.d.ts", "scripts": {"update": "npm install --legacy-peer-deps", "lib": "gulp build", "format": "eslint --fix dist/*.ts", "test": "npm run lib && jest"}, "devDependencies": {"@types/node": "20.10.0", "@babel/core": "^7.12.3", "@babel/plugin-transform-runtime": "^7.12.1", "@babel/preset-env": "^7.12.1", "@babel/runtime": "^7.12.5", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "del": "^6.0.0", "eslint": "^7.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-standard": "^16.0.1", "eslint-friendly-formatter": "^4.0.1", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.2", "eslint-plugin-typescript": "^0.14.0", "gulp": "^4.0.2", "gulp-autoprefixer": "^8.0.0", "gulp-babel": "^8.0.0", "gulp-clean-css": "^4.3.0", "gulp-concat": "^2.6.1", "gulp-rename": "^2.0.0", "gulp-replace": "^1.1.4", "gulp-sourcemaps": "^3.0.0", "gulp-typescript": "^5.0.1", "gulp-uglify": "^3.0.2", "jest": "^30.0.4", "typescript": "~4.7.4"}, "repository": {"type": "git", "url": "git+https://github.com/x-extends/xe-utils.git"}, "keywords": ["utils", "js-util", "js-tool", "xe-utils", "tools"], "author": {"name": "<PERSON>", "email": "xu_liang<PERSON><PERSON>@163.com"}, "license": "MIT", "bugs": {"url": "https://github.com/x-extends/xe-utils/issues"}, "homepage": "https://github.com/x-extends/xe-utils#readme", "postcss": {"plugins": {"autoprefixer": {}}}}