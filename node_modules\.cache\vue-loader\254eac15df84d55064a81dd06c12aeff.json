{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\views\\login.vue?vue&type=style&index=0&id=7589b93f&rel=stylesheet%2Fscss&lang=scss", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\views\\login.vue", "mtime": 1744789047407}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758071060223}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758071061987}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758071060880}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758071059610}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5sb2dpbiB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBoZWlnaHQ6IDEwMCU7DQogIGJhY2tncm91bmQtaW1hZ2U6IHVybCgiLi4vYXNzZXRzL2ltYWdlcy9sb2dpbi1iYWNrZ3JvdW5kLmpwZyIpOw0KICBiYWNrZ3JvdW5kLXNpemU6IGNvdmVyOw0KfQ0KLnRpdGxlIHsNCiAgbWFyZ2luOiAwcHggYXV0byAzMHB4IGF1dG87DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgY29sb3I6ICM3MDcwNzA7DQp9DQoNCi5sb2dpbi1mb3JtIHsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBiYWNrZ3JvdW5kOiAjZmZmZmZmOw0KICB3aWR0aDogNDAwcHg7DQogIHBhZGRpbmc6IDI1cHggMjVweCA1cHggMjVweDsNCiAgLmVsLWlucHV0IHsNCiAgICBoZWlnaHQ6IDM4cHg7DQogICAgaW5wdXQgew0KICAgICAgaGVpZ2h0OiAzOHB4Ow0KICAgIH0NCiAgfQ0KICAuaW5wdXQtaWNvbiB7DQogICAgaGVpZ2h0OiAzOXB4Ow0KICAgIHdpZHRoOiAxNHB4Ow0KICAgIG1hcmdpbi1sZWZ0OiAycHg7DQogIH0NCn0NCi5sb2dpbi10aXAgew0KICBmb250LXNpemU6IDEzcHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgY29sb3I6ICNiZmJmYmY7DQp9DQoubG9naW4tY29kZSB7DQogIHdpZHRoOiAzMyU7DQogIGhlaWdodDogMzhweDsNCiAgZmxvYXQ6IHJpZ2h0Ow0KICBpbWcgew0KICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlOw0KICB9DQp9DQouZWwtbG9naW4tZm9vdGVyIHsNCiAgaGVpZ2h0OiA0MHB4Ow0KICBsaW5lLWhlaWdodDogNDBweDsNCiAgcG9zaXRpb246IGZpeGVkOw0KICBib3R0b206IDA7DQogIHdpZHRoOiAxMDAlOw0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGNvbG9yOiAjZmZmOw0KICBmb250LWZhbWlseTogQXJpYWw7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgbGV0dGVyLXNwYWNpbmc6IDFweDsNCn0NCi5sb2dpbi1jb2RlLWltZyB7DQogIGhlaWdodDogMzhweDsNCn0NCi5sb2dpbl9ib3ggew0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBwYWRkaW5nLXRvcDogMjRweDsNCiAgLnRpdGxlIHsNCiAgICBtYXJnaW46IDBweCBhdXRvOw0KICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICBjb2xvcjogIzcwNzA3MDsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2NA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "login.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"login\">\r\n    <el-form\r\n      v-if=\"showLoginForm\"\r\n      ref=\"loginForm\"\r\n      :model=\"loginForm\"\r\n      :rules=\"loginRules\"\r\n      class=\"login-form\"\r\n    >\r\n      <h3 class=\"title\">开发区一网统管后台管理系统</h3>\r\n      <el-form-item prop=\"username\">\r\n        <el-input\r\n          v-model=\"loginForm.username\"\r\n          type=\"text\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"账号\"\r\n        >\r\n          <svg-icon\r\n            slot=\"prefix\"\r\n            icon-class=\"user\"\r\n            class=\"el-input__icon input-icon\"\r\n          />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"password\">\r\n        <el-input\r\n          v-model=\"loginForm.password\"\r\n          type=\"password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"密码\"\r\n          @keyup.enter.native=\"handleLogin\"\r\n        >\r\n          <svg-icon\r\n            slot=\"prefix\"\r\n            icon-class=\"password\"\r\n            class=\"el-input__icon input-icon\"\r\n          />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"code\">\r\n        <el-input\r\n          v-model=\"loginForm.code\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"验证码\"\r\n          style=\"width: 63%\"\r\n          @keyup.enter.native=\"handleLogin\"\r\n        >\r\n          <svg-icon\r\n            slot=\"prefix\"\r\n            icon-class=\"validCode\"\r\n            class=\"el-input__icon input-icon\"\r\n          />\r\n        </el-input>\r\n        <div class=\"login-code\">\r\n          <img :src=\"codeUrl\" @click=\"getCode\" class=\"login-code-img\" />\r\n        </div>\r\n      </el-form-item>\r\n      <el-checkbox\r\n        v-model=\"loginForm.rememberMe\"\r\n        style=\"margin: 0px 0px 25px 0px\"\r\n        >记住密码</el-checkbox\r\n      >\r\n      <el-form-item style=\"width: 100%\">\r\n        <el-button\r\n          :loading=\"loading\"\r\n          size=\"medium\"\r\n          type=\"primary\"\r\n          style=\"width: 100%\"\r\n          @click.native.prevent=\"handleLogin\"\r\n        >\r\n          <span v-if=\"!loading\">登 录</span>\r\n          <span v-else>登 录 中...</span>\r\n        </el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n    <div class=\"login_box\" v-else>\r\n      <h3 class=\"title\">开发区一网统管后台管理系统</h3>\r\n      <iframe\r\n        id=\"mainIframe\"\r\n        style=\"padding: 25px; padding-bottom: 5px\"\r\n        width=\"400px\"\r\n        height=\"350px\"\r\n        frameborder=\"0\"\r\n        scrolling=\"0\"\r\n        :src=\"scanUrl\"\r\n        sandbox=\"allow-scripts allow-top-navigation allow-same-origin\"\r\n      ></iframe>\r\n    </div>\r\n    <!--  底部  -->\r\n    <div class=\"el-login-footer\">\r\n      <span>Copyright © 2018-2021 ruoyi.vip All Rights Reserved.</span>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCodeImg } from \"@/api/login\";\r\nimport Cookies from \"js-cookie\";\r\nimport { encrypt, decrypt } from \"@/utils/jsencrypt\";\r\n\r\nexport default {\r\n  name: \"Login\",\r\n  data() {\r\n    return {\r\n      showLoginForm: false,\r\n      scanUrl: `${process.env.VUE_APP_SAOMA_CODE}/oauth2/auth.htm?response_type=code&client_id=${process.env.VUE_APP_CLIENT_ID}&redirect_uri=${process.env.VUE_APP_REDIRECT_URI}&scope=get_user_info&authType=QRCODE&embedMode=true`,\r\n      codeUrl: \"\",\r\n      cookiePassword: \"\",\r\n      loginForm: {\r\n        username: \"admin\",\r\n        password: \"admin123\",\r\n        rememberMe: false,\r\n        code: \"\",\r\n        uuid: \"\",\r\n      },\r\n      loginRules: {\r\n        username: [\r\n          { required: true, trigger: \"blur\", message: \"用户名不能为空\" },\r\n        ],\r\n        password: [\r\n          { required: true, trigger: \"blur\", message: \"密码不能为空\" },\r\n        ],\r\n        code: [\r\n          { required: true, trigger: \"change\", message: \"验证码不能为空\" },\r\n        ],\r\n      },\r\n      loading: false,\r\n      redirect: undefined,\r\n    };\r\n  },\r\n  watch: {\r\n    $route: {\r\n      handler: function (route) {\r\n        console.log(\"route\", route);\r\n        this.showLoginForm = route.fullPath.includes(\"user\");\r\n      },\r\n      immediate: true,\r\n    },\r\n  },\r\n  created() {\r\n    this.getCode();\r\n    this.getCookie();\r\n  },\r\n  mounted() {\r\n    window.addEventListener(\"message\", this.loginZZD);\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener(\"message\", this.loginZZD);\r\n  },\r\n  methods: {\r\n    // 浙政钉登录\r\n    loginZZD(event) {\r\n      const code = event.data && event.data.code;\r\n      console.log(\"code\", event);\r\n      if (code) {\r\n        console.log(\"code1111\", code);\r\n        this.$store\r\n          .dispatch(\"Login\", { zzdCode: code })\r\n          .then(() => {\r\n            this.$router.push({ path: this.redirect || \"/\" }).catch(() => {});\r\n          })\r\n          .catch(() => {\r\n            this.loading = false;\r\n            this.getCode();\r\n          });\r\n      }\r\n    },\r\n    getCode() {\r\n      getCodeImg().then((res) => {\r\n        this.codeUrl = \"data:image/gif;base64,\" + res.img;\r\n        this.loginForm.uuid = res.uuid;\r\n      });\r\n    },\r\n    getCookie() {\r\n      const username = Cookies.get(\"username\");\r\n      const password = Cookies.get(\"password\");\r\n      const rememberMe = Cookies.get(\"rememberMe\");\r\n      this.loginForm = {\r\n        username: username === undefined ? this.loginForm.username : username,\r\n        password:\r\n          password === undefined ? this.loginForm.password : decrypt(password),\r\n        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),\r\n      };\r\n    },\r\n    handleLogin() {\r\n      this.$refs.loginForm.validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          if (this.loginForm.rememberMe) {\r\n            Cookies.set(\"username\", this.loginForm.username, { expires: 30 });\r\n            Cookies.set(\"password\", encrypt(this.loginForm.password), {\r\n              expires: 30,\r\n            });\r\n            Cookies.set(\"rememberMe\", this.loginForm.rememberMe, {\r\n              expires: 30,\r\n            });\r\n          } else {\r\n            Cookies.remove(\"username\");\r\n            Cookies.remove(\"password\");\r\n            Cookies.remove(\"rememberMe\");\r\n          }\r\n          this.loginForm.password = encrypt(this.loginForm.password);\r\n          this.$store\r\n            .dispatch(\"Login\", this.loginForm)\r\n            .then(() => {\r\n              this.$router.push({ path: this.redirect || \"/\" }).catch(() => {});\r\n            })\r\n            .catch(() => {\r\n              this.loading = false;\r\n              this.getCode();\r\n            });\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\">\r\n.login {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  background-image: url(\"../assets/images/login-background.jpg\");\r\n  background-size: cover;\r\n}\r\n.title {\r\n  margin: 0px auto 30px auto;\r\n  text-align: center;\r\n  color: #707070;\r\n}\r\n\r\n.login-form {\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  width: 400px;\r\n  padding: 25px 25px 5px 25px;\r\n  .el-input {\r\n    height: 38px;\r\n    input {\r\n      height: 38px;\r\n    }\r\n  }\r\n  .input-icon {\r\n    height: 39px;\r\n    width: 14px;\r\n    margin-left: 2px;\r\n  }\r\n}\r\n.login-tip {\r\n  font-size: 13px;\r\n  text-align: center;\r\n  color: #bfbfbf;\r\n}\r\n.login-code {\r\n  width: 33%;\r\n  height: 38px;\r\n  float: right;\r\n  img {\r\n    cursor: pointer;\r\n    vertical-align: middle;\r\n  }\r\n}\r\n.el-login-footer {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  position: fixed;\r\n  bottom: 0;\r\n  width: 100%;\r\n  text-align: center;\r\n  color: #fff;\r\n  font-family: Arial;\r\n  font-size: 12px;\r\n  letter-spacing: 1px;\r\n}\r\n.login-code-img {\r\n  height: 38px;\r\n}\r\n.login_box {\r\n  background: #fff;\r\n  padding-top: 24px;\r\n  .title {\r\n    margin: 0px auto;\r\n    text-align: center;\r\n    color: #707070;\r\n  }\r\n}\r\n</style>\r\n"]}]}