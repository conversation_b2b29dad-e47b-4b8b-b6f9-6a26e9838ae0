{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\easyCron\\tabs\\minute.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\easyCron\\tabs\\minute.vue", "mtime": 1718070340306}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgbWl4aW4gZnJvbSAnLi9taXhpbicNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnbWludXRlJywNCiAgbWl4aW5zOiBbbWl4aW5dLA0KICBkYXRhICgpIHsNCiAgICByZXR1cm4ge30NCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICB2YWx1ZV9jIChuZXdWYWwsIG9sZFZhbCkgew0KICAgICAgdGhpcy4kZW1pdCgnY2hhbmdlJywgbmV3VmFsKQ0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCAoKSB7DQogICAgdGhpcy5ERUZBVUxUX1ZBTFVFID0gJyonDQogICAgdGhpcy5taW5WYWx1ZSA9IDANCiAgICB0aGlzLm1heFZhbHVlID0gNTkNCiAgICB0aGlzLnZhbHVlUmFuZ2Uuc3RhcnQgPSAwDQogICAgdGhpcy52YWx1ZVJhbmdlLmVuZCA9IDU5DQogICAgdGhpcy52YWx1ZUxvb3Auc3RhcnQgPSAwDQogICAgdGhpcy52YWx1ZUxvb3AuaW50ZXJ2YWwgPSAxDQogICAgdGhpcy5wYXJzZVByb3AodGhpcy5wcm9wKQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["minute.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "minute.vue", "sourceRoot": "src/components/easyCron/tabs", "sourcesContent": ["<template>\r\n  <div class=\"config-list\">\r\n    <RadioGroup v-model=\"type\">\r\n    <div class=\"item\">\r\n      <Radio label=\"TYPE_EVERY\" class=\"choice\" :disabled=\"disabled\">每分</Radio>\r\n    </div>\r\n    <div class=\"item\">\r\n      <Radio label=\"TYPE_RANGE\" class=\"choice\" :disabled=\"disabled\">区间</Radio>\r\n       从<InputNumber :disabled=\"type!=TYPE_RANGE || disabled\" :max=\"maxValue\" :min=\"minValue\" :precision=\"0\"\r\n        class=\"w60\" v-model=\"valueRange.start\" />分\r\n       至<InputNumber :disabled=\"type!=TYPE_RANGE || disabled\" :max=\"maxValue\" :min=\"minValue\" :precision=\"0\"\r\n        class=\"w60\" v-model=\"valueRange.end\" />分\r\n    </div>\r\n    <div class=\"item\">\r\n      <Radio label=\"TYPE_LOOP\" class=\"choice\" :disabled=\"disabled\">循环</Radio>\r\n      从<InputNumber :disabled=\"type!=TYPE_LOOP || disabled\" :max=\"maxValue\" :min=\"minValue\" :precision=\"0\"\r\n       class=\"w60\" v-model=\"valueLoop.start\" />分开始，间隔\r\n      <InputNumber :disabled=\"type!=TYPE_LOOP || disabled\" :max=\"maxValue\" :min=\"minValue\" :precision=\"0\"\r\n       class=\"w60\" v-model=\"valueLoop.interval\" />分\r\n    </div>\r\n    <div class=\"item\">\r\n      <Radio  label=\"TYPE_SPECIFY\" class=\"choice\" :disabled=\"disabled\">指定</Radio>\r\n      <div class=\"list\">\r\n        <CheckboxGroup v-model=\"valueList\">\r\n          <Checkbox class=\"list-check-item\" v-for=\"i in maxValue+1\"\r\n            :label=\"i-1\" :key=\"`key-${i-1}`\" :disabled=\"type!=TYPE_SPECIFY || disabled\"></Checkbox>\r\n        </CheckboxGroup>\r\n      </div>\r\n    </div>\r\n    </RadioGroup>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport mixin from './mixin'\r\n\r\nexport default {\r\n  name: 'minute',\r\n  mixins: [mixin],\r\n  data () {\r\n    return {}\r\n  },\r\n  watch: {\r\n    value_c (newVal, oldVal) {\r\n      this.$emit('change', newVal)\r\n    }\r\n  },\r\n  created () {\r\n    this.DEFAULT_VALUE = '*'\r\n    this.minValue = 0\r\n    this.maxValue = 59\r\n    this.valueRange.start = 0\r\n    this.valueRange.end = 59\r\n    this.valueLoop.start = 0\r\n    this.valueLoop.interval = 1\r\n    this.parseProp(this.prop)\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n.config-list {\r\n  text-align: left;\r\n  margin: 0 10px 10px 10px;\r\n}\r\n\r\n.item {\r\n  margin-top: 5px;\r\n}\r\n\r\n.choice {\r\n  border: 1px solid transparent;\r\n  padding: 5px 8px;\r\n}\r\n\r\n.choice:hover {\r\n  border: 1px solid #1890ff;\r\n}\r\n\r\n.w60 {\r\n  width: 60px;\r\n}\r\n\r\n.ivu-input-number {\r\n  margin-left: 5px;\r\n  margin-right: 5px;\r\n}\r\n\r\n.list {\r\n  margin: 0 20px;\r\n}\r\n\r\n.list-check-item {\r\n  padding: 1px 3px;\r\n  width: 4em;\r\n}\r\n</style>\r\n"]}]}