{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\views\\monitor\\cache\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\views\\monitor\\cache\\index.vue", "mtime": 1718070340326}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/monitor/cache", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"24\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\"><span>基本信息</span></div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <table cellspacing=\"0\" style=\"width: 100%\">\r\n              <tbody>\r\n                <tr>\r\n                  <td><div class=\"cell\">Redis版本</div></td>\r\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.redis_version }}</div></td>\r\n                  <td><div class=\"cell\">运行模式</div></td>\r\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.redis_mode == \"standalone\" ? \"单机\" : \"集群\" }}</div></td>\r\n                  <td><div class=\"cell\">端口</div></td>\r\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.tcp_port }}</div></td>\r\n                  <td><div class=\"cell\">客户端数</div></td>\r\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.connected_clients }}</div></td>\r\n                </tr>\r\n                <tr>\r\n                  <td><div class=\"cell\">运行时间(天)</div></td>\r\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.uptime_in_days }}</div></td>\r\n                  <td><div class=\"cell\">使用内存</div></td>\r\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.used_memory_human }}</div></td>\r\n                  <td><div class=\"cell\">使用CPU</div></td>\r\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ parseFloat(cache.info.used_cpu_user_children).toFixed(2) }}</div></td>\r\n                  <td><div class=\"cell\">内存配置</div></td>\r\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.maxmemory_human }}</div></td>\r\n                </tr>\r\n                <tr>\r\n                  <td><div class=\"cell\">AOF是否开启</div></td>\r\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.aof_enabled == \"0\" ? \"否\" : \"是\" }}</div></td>\r\n                  <td><div class=\"cell\">RDB是否成功</div></td>\r\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.rdb_last_bgsave_status }}</div></td>\r\n                  <td><div class=\"cell\">Key数量</div></td>\r\n                  <td><div class=\"cell\" v-if=\"cache.dbSize\">{{ cache.dbSize }} </div></td>\r\n                  <td><div class=\"cell\">网络入口/出口</div></td>\r\n                  <td><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.instantaneous_input_kbps }}kps/{{cache.info.instantaneous_output_kbps}}kps</div></td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\"><span>命令统计</span></div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <div ref=\"commandstats\" style=\"height: 420px\" />\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\">\r\n            <span>内存信息</span>\r\n          </div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <div ref=\"usedmemory\" style=\"height: 420px\" />\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCache } from \"@/api/monitor/cache\";\r\nimport echarts from \"echarts\";\r\n\r\nexport default {\r\n  name: \"Server\",\r\n  data() {\r\n    return {\r\n      // 加载层信息\r\n      loading: [],\r\n      // 统计命令信息\r\n      commandstats: null,\r\n      // 使用内存\r\n      usedmemory: null,\r\n      // cache信息\r\n      cache: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.openLoading();\r\n  },\r\n  methods: {\r\n    /** 查缓存询信息 */\r\n    getList() {\r\n      getCache().then((response) => {\r\n        this.cache = response.data;\r\n        this.loading.close();\r\n\r\n        this.commandstats = echarts.init(this.$refs.commandstats, \"macarons\");\r\n        this.commandstats.setOption({\r\n          tooltip: {\r\n            trigger: \"item\",\r\n            formatter: \"{a} <br/>{b} : {c} ({d}%)\",\r\n          },\r\n          series: [\r\n            {\r\n              name: \"命令\",\r\n              type: \"pie\",\r\n              roseType: \"radius\",\r\n              radius: [15, 95],\r\n              center: [\"50%\", \"38%\"],\r\n              data: response.data.commandStats,\r\n              animationEasing: \"cubicInOut\",\r\n              animationDuration: 1000,\r\n            },\r\n          ],\r\n        });\r\n        this.usedmemory = echarts.init(this.$refs.usedmemory, \"macarons\");\r\n        this.usedmemory.setOption({\r\n          tooltip: {\r\n            formatter: \"{b} <br/>{a} : \" + this.cache.info.used_memory_human,\r\n          },\r\n          series: [\r\n            {\r\n              name: \"峰值\",\r\n              type: \"gauge\",\r\n              min: 0,\r\n              max: 1000,\r\n              detail: {\r\n                formatter: this.cache.info.used_memory_human,\r\n              },\r\n              data: [\r\n                {\r\n                  value: parseFloat(this.cache.info.used_memory_human),\r\n                  name: \"内存消耗\",\r\n                },\r\n              ],\r\n            },\r\n          ],\r\n        });\r\n      });\r\n    },\r\n    // 打开加载层\r\n    openLoading() {\r\n      this.loading = this.$loading({\r\n        lock: true,\r\n        text: \"拼命读取中\",\r\n        spinner: \"el-icon-loading\",\r\n        background: \"rgba(0, 0, 0, 0.7)\",\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n"]}]}