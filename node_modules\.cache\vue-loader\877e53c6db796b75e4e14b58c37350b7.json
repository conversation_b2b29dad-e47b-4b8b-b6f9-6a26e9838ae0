{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\Breadcrumb\\index.vue?vue&type=style&index=0&id=b50ef614&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\Breadcrumb\\index.vue", "mtime": 1718070340298}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758071060223}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758071061987}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758071060880}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758071059610}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmFwcC1icmVhZGNydW1iLmVsLWJyZWFkY3J1bWIgew0KICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgbGluZS1oZWlnaHQ6IDUwcHg7DQogIG1hcmdpbi1sZWZ0OiA4cHg7DQoNCiAgLm5vLXJlZGlyZWN0IHsNCiAgICBjb2xvcjogIzk3YThiZTsNCiAgICBjdXJzb3I6IHRleHQ7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Breadcrumb", "sourcesContent": ["<template>\r\n  <el-breadcrumb class=\"app-breadcrumb\" separator=\"/\">\r\n    <transition-group name=\"breadcrumb\">\r\n      <el-breadcrumb-item v-for=\"(item,index) in levelList\" :key=\"item.path\">\r\n        <span v-if=\"item.redirect==='noRedirect'||index==levelList.length-1\" class=\"no-redirect\">{{ item.meta.title }}</span>\r\n        <a v-else @click.prevent=\"handleLink(item)\">{{ item.meta.title }}</a>\r\n      </el-breadcrumb-item>\r\n    </transition-group>\r\n  </el-breadcrumb>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      levelList: null\r\n    }\r\n  },\r\n  watch: {\r\n    $route(route) {\r\n      // if you go to the redirect page, do not update the breadcrumbs\r\n      if (route.path.startsWith('/redirect/')) {\r\n        return\r\n      }\r\n      this.getBreadcrumb()\r\n    }\r\n  },\r\n  created() {\r\n    this.getBreadcrumb()\r\n  },\r\n  methods: {\r\n    getBreadcrumb() {\r\n      // only show routes with meta.title\r\n      let matched = this.$route.matched.filter(item => item.meta && item.meta.title)\r\n      const first = matched[0]\r\n\r\n      if (!this.isDashboard(first)) {\r\n        matched = [{ path: '/index', meta: { title: '首页' }}].concat(matched)\r\n      }\r\n\r\n      this.levelList = matched.filter(item => item.meta && item.meta.title && item.meta.breadcrumb !== false)\r\n    },\r\n    isDashboard(route) {\r\n      const name = route && route.name\r\n      if (!name) {\r\n        return false\r\n      }\r\n      return name.trim() === '首页'\r\n    },\r\n    handleLink(item) {\r\n      const { redirect, path } = item\r\n      if (redirect) {\r\n        this.$router.push(redirect)\r\n        return\r\n      }\r\n      this.$router.push(path)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-breadcrumb.el-breadcrumb {\r\n  display: inline-block;\r\n  font-size: 14px;\r\n  line-height: 50px;\r\n  margin-left: 8px;\r\n\r\n  .no-redirect {\r\n    color: #97a8be;\r\n    cursor: text;\r\n  }\r\n}\r\n</style>\r\n"]}]}