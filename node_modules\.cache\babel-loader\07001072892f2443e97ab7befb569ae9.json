{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\ThemePicker\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\ThemePicker\\index.vue", "mtime": 1718070340305}, {"path": "D:\\project_about\\git174\\alznt-admin\\babel.config.js", "mtime": 1718238659557}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["version", "require", "ORIGINAL_THEME", "_default", "exports", "default", "data", "chalk", "theme", "computed", "defaultTheme", "$store", "state", "settings", "watch", "handler", "val", "oldVal", "immediate", "_this", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "themeCluster", "originalCluster", "$message", "<PERSON><PERSON><PERSON><PERSON>", "url", "<PERSON><PERSON><PERSON><PERSON>", "styles", "w", "_context", "n", "a", "getThemeCluster", "replace", "console", "log", "message", "customClass", "type", "duration", "iconClass", "variable", "id", "newStyle", "updateStyle", "styleTag", "document", "getElementById", "createElement", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "innerText", "concat", "getCSSString", "slice", "call", "querySelectorAll", "filter", "style", "text", "RegExp", "test", "for<PERSON>ach", "$emit", "close", "methods", "oldCluster", "newCluster", "color", "index", "_this2", "Promise", "resolve", "xhr", "XMLHttpRequest", "onreadystatechange", "readyState", "status", "responseText", "open", "send", "tintColor", "tint", "red", "parseInt", "green", "blue", "join", "Math", "round", "toString", "shadeColor", "shade", "clusters", "i", "push", "Number", "toFixed"], "sources": ["src/components/ThemePicker/index.vue"], "sourcesContent": ["<template>\r\n  <el-color-picker\r\n    v-model=\"theme\"\r\n    :predefine=\"['#409EFF', '#1890ff', '#304156','#212121','#11a983', '#13c2c2', '#6959CD', '#f5222d', ]\"\r\n    class=\"theme-picker\"\r\n    popper-class=\"theme-picker-dropdown\"\r\n  />\r\n</template>\r\n\r\n<script>\r\nconst version = require('element-ui/package.json').version // element-ui version from node_modules\r\nconst ORIGINAL_THEME = '#409EFF' // default color\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      chalk: '', // content of theme-chalk css\r\n      theme: ''\r\n    }\r\n  },\r\n  computed: {\r\n    defaultTheme() {\r\n      return this.$store.state.settings.theme\r\n    }\r\n  },\r\n  watch: {\r\n    defaultTheme: {\r\n      handler: function(val, oldVal) {\r\n        this.theme = val\r\n      },\r\n      immediate: true\r\n    },\r\n    async theme(val) {\r\n      const oldVal = this.chalk ? this.theme : ORIGINAL_THEME\r\n      if (typeof val !== 'string') return\r\n      const themeCluster = this.getThemeCluster(val.replace('#', ''))\r\n      const originalCluster = this.getThemeCluster(oldVal.replace('#', ''))\r\n      console.log(themeCluster, originalCluster)\r\n\r\n      const $message = this.$message({\r\n        message: '  Compiling the theme',\r\n        customClass: 'theme-message',\r\n        type: 'success',\r\n        duration: 0,\r\n        iconClass: 'el-icon-loading'\r\n      })\r\n\r\n      const getHandler = (variable, id) => {\r\n        return () => {\r\n          const originalCluster = this.getThemeCluster(ORIGINAL_THEME.replace('#', ''))\r\n          const newStyle = this.updateStyle(this[variable], originalCluster, themeCluster)\r\n\r\n          let styleTag = document.getElementById(id)\r\n          if (!styleTag) {\r\n            styleTag = document.createElement('style')\r\n            styleTag.setAttribute('id', id)\r\n            document.head.appendChild(styleTag)\r\n          }\r\n          styleTag.innerText = newStyle\r\n        }\r\n      }\r\n\r\n      if (!this.chalk) {\r\n        const url = `https://unpkg.com/element-ui@${version}/lib/theme-chalk/index.css`\r\n        await this.getCSSString(url, 'chalk')\r\n      }\r\n\r\n      const chalkHandler = getHandler('chalk', 'chalk-style')\r\n\r\n      chalkHandler()\r\n\r\n      const styles = [].slice.call(document.querySelectorAll('style'))\r\n        .filter(style => {\r\n          const text = style.innerText\r\n          return new RegExp(oldVal, 'i').test(text) && !/Chalk Variables/.test(text)\r\n        })\r\n      styles.forEach(style => {\r\n        const { innerText } = style\r\n        if (typeof innerText !== 'string') return\r\n        style.innerText = this.updateStyle(innerText, originalCluster, themeCluster)\r\n      })\r\n\r\n      this.$emit('change', val)\r\n\r\n      $message.close()\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    updateStyle(style, oldCluster, newCluster) {\r\n      let newStyle = style\r\n      oldCluster.forEach((color, index) => {\r\n        newStyle = newStyle.replace(new RegExp(color, 'ig'), newCluster[index])\r\n      })\r\n      return newStyle\r\n    },\r\n\r\n    getCSSString(url, variable) {\r\n      return new Promise(resolve => {\r\n        const xhr = new XMLHttpRequest()\r\n        xhr.onreadystatechange = () => {\r\n          if (xhr.readyState === 4 && xhr.status === 200) {\r\n            this[variable] = xhr.responseText.replace(/@font-face{[^}]+}/, '')\r\n            resolve()\r\n          }\r\n        }\r\n        xhr.open('GET', url)\r\n        xhr.send()\r\n      })\r\n    },\r\n\r\n    getThemeCluster(theme) {\r\n      const tintColor = (color, tint) => {\r\n        let red = parseInt(color.slice(0, 2), 16)\r\n        let green = parseInt(color.slice(2, 4), 16)\r\n        let blue = parseInt(color.slice(4, 6), 16)\r\n\r\n        if (tint === 0) { // when primary color is in its rgb space\r\n          return [red, green, blue].join(',')\r\n        } else {\r\n          red += Math.round(tint * (255 - red))\r\n          green += Math.round(tint * (255 - green))\r\n          blue += Math.round(tint * (255 - blue))\r\n\r\n          red = red.toString(16)\r\n          green = green.toString(16)\r\n          blue = blue.toString(16)\r\n\r\n          return `#${red}${green}${blue}`\r\n        }\r\n      }\r\n\r\n      const shadeColor = (color, shade) => {\r\n        let red = parseInt(color.slice(0, 2), 16)\r\n        let green = parseInt(color.slice(2, 4), 16)\r\n        let blue = parseInt(color.slice(4, 6), 16)\r\n\r\n        red = Math.round((1 - shade) * red)\r\n        green = Math.round((1 - shade) * green)\r\n        blue = Math.round((1 - shade) * blue)\r\n\r\n        red = red.toString(16)\r\n        green = green.toString(16)\r\n        blue = blue.toString(16)\r\n\r\n        return `#${red}${green}${blue}`\r\n      }\r\n\r\n      const clusters = [theme]\r\n      for (let i = 0; i <= 9; i++) {\r\n        clusters.push(tintColor(theme, Number((i / 10).toFixed(2))))\r\n      }\r\n      clusters.push(shadeColor(theme, 0.1))\r\n      return clusters\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.theme-message,\r\n.theme-picker-dropdown {\r\n  z-index: 99999 !important;\r\n}\r\n\r\n.theme-picker .el-color-picker__trigger {\r\n  height: 26px !important;\r\n  width: 26px !important;\r\n  padding: 2px;\r\n}\r\n\r\n.theme-picker-dropdown .el-color-dropdown__link-btn {\r\n  display: none;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAAA,OAAA,GAAAC,OAAA,4BAAAD,OAAA;AACA,IAAAE,cAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;MAAA;MACAC,KAAA;IACA;EACA;EACAC,QAAA;IACAC,YAAA,WAAAA,aAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAL,KAAA;IACA;EACA;EACAM,KAAA;IACAJ,YAAA;MACAK,OAAA,WAAAA,QAAAC,GAAA,EAAAC,MAAA;QACA,KAAAT,KAAA,GAAAQ,GAAA;MACA;MACAE,SAAA;IACA;IACAV,KAAA,WAAAA,MAAAQ,GAAA;MAAA,IAAAG,KAAA;MAAA,WAAAC,kBAAA,CAAAf,OAAA,mBAAAgB,aAAA,CAAAhB,OAAA,IAAAiB,CAAA,UAAAC,QAAA;QAAA,IAAAN,MAAA,EAAAO,YAAA,EAAAC,eAAA,EAAAC,QAAA,EAAAC,UAAA,EAAAC,GAAA,EAAAC,YAAA,EAAAC,MAAA;QAAA,WAAAT,aAAA,CAAAhB,OAAA,IAAA0B,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cACAhB,MAAA,GAAAE,KAAA,CAAAZ,KAAA,GAAAY,KAAA,CAAAX,KAAA,GAAAN,cAAA;cAAA,MACA,OAAAc,GAAA;gBAAAgB,QAAA,CAAAC,CAAA;gBAAA;cAAA;cAAA,OAAAD,QAAA,CAAAE,CAAA;YAAA;cACAV,YAAA,GAAAL,KAAA,CAAAgB,eAAA,CAAAnB,GAAA,CAAAoB,OAAA;cACAX,eAAA,GAAAN,KAAA,CAAAgB,eAAA,CAAAlB,MAAA,CAAAmB,OAAA;cACAC,OAAA,CAAAC,GAAA,CAAAd,YAAA,EAAAC,eAAA;cAEAC,QAAA,GAAAP,KAAA,CAAAO,QAAA;gBACAa,OAAA;gBACAC,WAAA;gBACAC,IAAA;gBACAC,QAAA;gBACAC,SAAA;cACA;cAEAhB,UAAA,YAAAA,WAAAiB,QAAA,EAAAC,EAAA;gBACA;kBACA,IAAApB,eAAA,GAAAN,KAAA,CAAAgB,eAAA,CAAAjC,cAAA,CAAAkC,OAAA;kBACA,IAAAU,QAAA,GAAA3B,KAAA,CAAA4B,WAAA,CAAA5B,KAAA,CAAAyB,QAAA,GAAAnB,eAAA,EAAAD,YAAA;kBAEA,IAAAwB,QAAA,GAAAC,QAAA,CAAAC,cAAA,CAAAL,EAAA;kBACA,KAAAG,QAAA;oBACAA,QAAA,GAAAC,QAAA,CAAAE,aAAA;oBACAH,QAAA,CAAAI,YAAA,OAAAP,EAAA;oBACAI,QAAA,CAAAI,IAAA,CAAAC,WAAA,CAAAN,QAAA;kBACA;kBACAA,QAAA,CAAAO,SAAA,GAAAT,QAAA;gBACA;cACA;cAAA,IAEA3B,KAAA,CAAAZ,KAAA;gBAAAyB,QAAA,CAAAC,CAAA;gBAAA;cAAA;cACAL,GAAA,mCAAA4B,MAAA,CAAAxD,OAAA;cAAAgC,QAAA,CAAAC,CAAA;cAAA,OACAd,KAAA,CAAAsC,YAAA,CAAA7B,GAAA;YAAA;cAGAC,YAAA,GAAAF,UAAA;cAEAE,YAAA;cAEAC,MAAA,MAAA4B,KAAA,CAAAC,IAAA,CAAAV,QAAA,CAAAW,gBAAA,WACAC,MAAA,WAAAC,KAAA;gBACA,IAAAC,IAAA,GAAAD,KAAA,CAAAP,SAAA;gBACA,WAAAS,MAAA,CAAA/C,MAAA,OAAAgD,IAAA,CAAAF,IAAA,wBAAAE,IAAA,CAAAF,IAAA;cACA;cACAjC,MAAA,CAAAoC,OAAA,WAAAJ,KAAA;gBACA,IAAAP,SAAA,GAAAO,KAAA,CAAAP,SAAA;gBACA,WAAAA,SAAA;gBACAO,KAAA,CAAAP,SAAA,GAAApC,KAAA,CAAA4B,WAAA,CAAAQ,SAAA,EAAA9B,eAAA,EAAAD,YAAA;cACA;cAEAL,KAAA,CAAAgD,KAAA,WAAAnD,GAAA;cAEAU,QAAA,CAAA0C,KAAA;YAAA;cAAA,OAAApC,QAAA,CAAAE,CAAA;UAAA;QAAA,GAAAX,OAAA;MAAA;IACA;EACA;EAEA8C,OAAA;IACAtB,WAAA,WAAAA,YAAAe,KAAA,EAAAQ,UAAA,EAAAC,UAAA;MACA,IAAAzB,QAAA,GAAAgB,KAAA;MACAQ,UAAA,CAAAJ,OAAA,WAAAM,KAAA,EAAAC,KAAA;QACA3B,QAAA,GAAAA,QAAA,CAAAV,OAAA,KAAA4B,MAAA,CAAAQ,KAAA,SAAAD,UAAA,CAAAE,KAAA;MACA;MACA,OAAA3B,QAAA;IACA;IAEAW,YAAA,WAAAA,aAAA7B,GAAA,EAAAgB,QAAA;MAAA,IAAA8B,MAAA;MACA,WAAAC,OAAA,WAAAC,OAAA;QACA,IAAAC,GAAA,OAAAC,cAAA;QACAD,GAAA,CAAAE,kBAAA;UACA,IAAAF,GAAA,CAAAG,UAAA,UAAAH,GAAA,CAAAI,MAAA;YACAP,MAAA,CAAA9B,QAAA,IAAAiC,GAAA,CAAAK,YAAA,CAAA9C,OAAA;YACAwC,OAAA;UACA;QACA;QACAC,GAAA,CAAAM,IAAA,QAAAvD,GAAA;QACAiD,GAAA,CAAAO,IAAA;MACA;IACA;IAEAjD,eAAA,WAAAA,gBAAA3B,KAAA;MACA,IAAA6E,SAAA,YAAAA,UAAAb,KAAA,EAAAc,IAAA;QACA,IAAAC,GAAA,GAAAC,QAAA,CAAAhB,KAAA,CAAAd,KAAA;QACA,IAAA+B,KAAA,GAAAD,QAAA,CAAAhB,KAAA,CAAAd,KAAA;QACA,IAAAgC,IAAA,GAAAF,QAAA,CAAAhB,KAAA,CAAAd,KAAA;QAEA,IAAA4B,IAAA;UAAA;UACA,QAAAC,GAAA,EAAAE,KAAA,EAAAC,IAAA,EAAAC,IAAA;QACA;UACAJ,GAAA,IAAAK,IAAA,CAAAC,KAAA,CAAAP,IAAA,UAAAC,GAAA;UACAE,KAAA,IAAAG,IAAA,CAAAC,KAAA,CAAAP,IAAA,UAAAG,KAAA;UACAC,IAAA,IAAAE,IAAA,CAAAC,KAAA,CAAAP,IAAA,UAAAI,IAAA;UAEAH,GAAA,GAAAA,GAAA,CAAAO,QAAA;UACAL,KAAA,GAAAA,KAAA,CAAAK,QAAA;UACAJ,IAAA,GAAAA,IAAA,CAAAI,QAAA;UAEA,WAAAtC,MAAA,CAAA+B,GAAA,EAAA/B,MAAA,CAAAiC,KAAA,EAAAjC,MAAA,CAAAkC,IAAA;QACA;MACA;MAEA,IAAAK,UAAA,YAAAA,WAAAvB,KAAA,EAAAwB,KAAA;QACA,IAAAT,GAAA,GAAAC,QAAA,CAAAhB,KAAA,CAAAd,KAAA;QACA,IAAA+B,KAAA,GAAAD,QAAA,CAAAhB,KAAA,CAAAd,KAAA;QACA,IAAAgC,IAAA,GAAAF,QAAA,CAAAhB,KAAA,CAAAd,KAAA;QAEA6B,GAAA,GAAAK,IAAA,CAAAC,KAAA,MAAAG,KAAA,IAAAT,GAAA;QACAE,KAAA,GAAAG,IAAA,CAAAC,KAAA,MAAAG,KAAA,IAAAP,KAAA;QACAC,IAAA,GAAAE,IAAA,CAAAC,KAAA,MAAAG,KAAA,IAAAN,IAAA;QAEAH,GAAA,GAAAA,GAAA,CAAAO,QAAA;QACAL,KAAA,GAAAA,KAAA,CAAAK,QAAA;QACAJ,IAAA,GAAAA,IAAA,CAAAI,QAAA;QAEA,WAAAtC,MAAA,CAAA+B,GAAA,EAAA/B,MAAA,CAAAiC,KAAA,EAAAjC,MAAA,CAAAkC,IAAA;MACA;MAEA,IAAAO,QAAA,IAAAzF,KAAA;MACA,SAAA0F,CAAA,MAAAA,CAAA,OAAAA,CAAA;QACAD,QAAA,CAAAE,IAAA,CAAAd,SAAA,CAAA7E,KAAA,EAAA4F,MAAA,EAAAF,CAAA,OAAAG,OAAA;MACA;MACAJ,QAAA,CAAAE,IAAA,CAAAJ,UAAA,CAAAvF,KAAA;MACA,OAAAyF,QAAA;IACA;EACA;AACA", "ignoreList": []}]}