{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\SelectTree\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\SelectTree\\index.vue", "mtime": 1740448089611}, {"path": "D:\\project_about\\git174\\alznt-admin\\babel.config.js", "mtime": 1718238659557}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "props", "type", "Object", "default", "value", "children", "label", "disabled", "Boolean", "options", "Array", "String", "Number", "clearable", "accordion", "multiple", "data", "localValue", "watch", "immediate", "handler", "newValue", "$emit", "methods", "clearH<PERSON>le", "clearSelected", "handleNodeClick", "node", "includes", "push", "treeNodes", "$refs", "selectTree", "$el", "querySelectorAll", "for<PERSON>ach", "classList", "remove"], "sources": ["src/components/SelectTree/index.vue"], "sourcesContent": ["<!-- el-select & el-tree 下拉树形选择 -->\r\n<template>\r\n  <div>\r\n    <el-select ref=\"selectTree\" v-model=\"localValue\" :multiple=\"multiple\" :clearable=\"clearable\" @clear=\"clearHandle\" :disabled=\"disabled\">\r\n      <el-option v-for=\"option in options\" :key=\"option[props.value]\" :value=\"option[props.value]\">\r\n        <el-tree\r\n          :data=\"options\"\r\n          :props=\"props\"\r\n          :node-key=\"props.value\"\r\n          @node-click=\"handleNodeClick\"\r\n          :accordion=\"accordion\"\r\n        >\r\n          <span slot-scope=\"{ data }\">\r\n            <i :class=\"[data.color != null ? 'ification_col' : '']\" :style=\"{'background-color': data.color}\"></i>&nbsp;&nbsp;{{ data.label }}\r\n          </span>\r\n        </el-tree>\r\n      </el-option>\r\n    </el-select>\r\n  </div>\r\n</template>\r\n\r\n<script>export default {\r\n  name: \"el-tree-select\",\r\n  props: {\r\n    // 配置项\r\n    props: {\r\n      type: Object,\r\n      default: () => ({\r\n        value: 'id',\r\n        children: 'children',\r\n        label: 'name'\r\n      })\r\n    },\r\n    // 是否禁用\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 选项列表数据(树形结构的对象数组)\r\n    options: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 初始值（单选或多选）\r\n    value: {\r\n      type: [String, Number, Object, Array], // 允许多种类型\r\n      default: null\r\n    },\r\n    // 可清空选项\r\n    clearable: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    // 自动收起\r\n    accordion: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 是否多选\r\n    multiple: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      localValue: this.value || (this.multiple ? [] : null), // 使用 value 或者默认值\r\n    }\r\n  },\r\n  watch: {\r\n    value: {\r\n      immediate: true,\r\n      handler(newValue) {\r\n        this.localValue = newValue;\r\n      }\r\n    },\r\n    localValue: {\r\n      handler(newValue) {\r\n        this.$emit('input', newValue);\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    // 清除选中\r\n    clearHandle() {\r\n      this.localValue = this.multiple ? [] : null;\r\n      this.clearSelected();\r\n      this.$emit('input', this.localValue);\r\n    },\r\n    // 切换选项\r\n    handleNodeClick(node) {\r\n      if (this.multiple) {\r\n        // 多选（判重后添加）\r\n        if (!this.localValue.includes(node[this.props.label])) {\r\n          this.localValue.push(node[this.props.label]);\r\n        }\r\n      } else {\r\n        // 单选\r\n        this.localValue = node[this.props.label];\r\n      }\r\n      this.$emit('input', this.localValue);\r\n    },\r\n    // 清空选中样式\r\n    clearSelected() {\r\n      const treeNodes = this.$refs.selectTree.$el.querySelectorAll('.el-tree-node');\r\n      treeNodes.forEach(node => node.classList.remove('is-current'));\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style scoped>\r\n.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {\r\n  height: auto;\r\n  max-height: 300px;\r\n  padding: 0;\r\n  overflow: hidden;\r\n  overflow-y: auto;\r\n}\r\n.el-select-dropdown__item.selected {\r\n  font-weight: normal;\r\n}\r\n.el-tree .el-tree-node__content {\r\n  height: auto;\r\n  padding: 0 20px;\r\n}\r\n.el-tree-node__label {\r\n  font-weight: normal;\r\n}\r\n.el-tree >>> .is-current .el-tree-node__label {\r\n  color: #409EFF;\r\n  font-weight: 700;\r\n}\r\n.el-tree >>> .is-current .el-tree-node__children .el-tree-node__label {\r\n  color: #606266;\r\n  font-weight: normal;\r\n}\r\n.el-popper {\r\n  z-index: 9999;\r\n}\r\n.ification_col {\r\n  width: 20px;\r\n  height: 10px;\r\n  display: inline-block;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAqBA;EACAA,IAAA;EACAC,KAAA;IACA;IACAA,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;UACAC,KAAA;UACAC,QAAA;UACAC,KAAA;QACA;MAAA;IACA;IACA;IACAC,QAAA;MACAN,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACA;IACAM,OAAA;MACAR,IAAA,EAAAS,KAAA;MACAP,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACA;IACAC,KAAA;MACAH,IAAA,GAAAU,MAAA,EAAAC,MAAA,EAAAV,MAAA,EAAAQ,KAAA;MAAA;MACAP,OAAA;IACA;IACA;IACAU,SAAA;MACAZ,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACA;IACAW,SAAA;MACAb,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACA;IACAY,QAAA;MACAd,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;EACA;EACAa,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA,OAAAb,KAAA,UAAAW,QAAA;IACA;EACA;EACAG,KAAA;IACAd,KAAA;MACAe,SAAA;MACAC,OAAA,WAAAA,QAAAC,QAAA;QACA,KAAAJ,UAAA,GAAAI,QAAA;MACA;IACA;IACAJ,UAAA;MACAG,OAAA,WAAAA,QAAAC,QAAA;QACA,KAAAC,KAAA,UAAAD,QAAA;MACA;IACA;EACA;EACAE,OAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAP,UAAA,QAAAF,QAAA;MACA,KAAAU,aAAA;MACA,KAAAH,KAAA,eAAAL,UAAA;IACA;IACA;IACAS,eAAA,WAAAA,gBAAAC,IAAA;MACA,SAAAZ,QAAA;QACA;QACA,UAAAE,UAAA,CAAAW,QAAA,CAAAD,IAAA,MAAA3B,KAAA,CAAAM,KAAA;UACA,KAAAW,UAAA,CAAAY,IAAA,CAAAF,IAAA,MAAA3B,KAAA,CAAAM,KAAA;QACA;MACA;QACA;QACA,KAAAW,UAAA,GAAAU,IAAA,MAAA3B,KAAA,CAAAM,KAAA;MACA;MACA,KAAAgB,KAAA,eAAAL,UAAA;IACA;IACA;IACAQ,aAAA,WAAAA,cAAA;MACA,IAAAK,SAAA,QAAAC,KAAA,CAAAC,UAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAJ,SAAA,CAAAK,OAAA,WAAAR,IAAA;QAAA,OAAAA,IAAA,CAAAS,SAAA,CAAAC,MAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}