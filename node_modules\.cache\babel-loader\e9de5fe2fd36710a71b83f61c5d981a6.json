{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project_about\\git174\\alznt-admin\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\project_about\\git174\\alznt-admin\\src\\api\\screen\\EventTask\\task.js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\api\\screen\\EventTask\\task.js", "mtime": 1740448089609}, {"path": "D:\\project_about\\git174\\alznt-admin\\babel.config.js", "mtime": 1718238659557}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1758071059938}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listTask", "query", "request", "url", "method", "params", "getTask", "id", "addTask", "data", "updateTask", "delTask", "FinishTask", "concat", "exportTask", "getTaskChart"], "sources": ["D:/project_about/git174/alznt-admin/src/api/screen/EventTask/task.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询事件管理-任务库列表\r\nexport function listTask(query) {\r\n  return request({\r\n    url: '/kfqywtg/task/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询事件管理-任务库详细\r\nexport function getTask(id) {\r\n  return request({\r\n    url: '/kfqywtg/task/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增事件管理-任务库\r\nexport function addTask(data) {\r\n  return request({\r\n    url: '/kfqywtg/task',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改事件管理-任务库\r\nexport function updateTask(data) {\r\n  return request({\r\n    url: '/kfqywtg/task',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除事件管理-任务库\r\nexport function delTask(id) {\r\n  return request({\r\n    url: '/kfqywtg/task/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 完成事件管理-任务库\r\nexport function FinishTask(id) {\r\n  return request({\r\n    url: `/kfqywtg/task/complete/${id}`,\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n// 导出事件管理-任务库\r\nexport function exportTask(query) {\r\n  return request({\r\n    url: '/kfqywtg/task/export',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询任务图表数据\r\nexport function getTaskChart(query) {\r\n  return request({\r\n    url: '/kfqywtg/task/staticStreet',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,EAAE,EAAE;EAC1B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB,GAAGI,EAAE;IAC1BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACJ,EAAE,EAAE;EAC1B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB,GAAGI,EAAE;IAC1BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,UAAUA,CAACL,EAAE,EAAE;EAC7B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,4BAAAU,MAAA,CAA4BN,EAAE,CAAE;IACnCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,UAAUA,CAACb,KAAK,EAAE;EAChC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASc,YAAYA,CAACd,KAAK,EAAE;EAClC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}