{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project_about\\git174\\alznt-admin\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\project_about\\git174\\alznt-admin\\src\\api\\screen\\EventTask\\event.js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\api\\screen\\EventTask\\event.js", "mtime": 1740448089608}, {"path": "D:\\project_about\\git174\\alznt-admin\\babel.config.js", "mtime": 1718238659557}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1758071059938}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9wcm9qZWN0X2Fib3V0L2dpdDE3NC9hbHpudC1hZG1pbi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuYWRkRXZlbnQgPSBhZGRFdmVudDsKZXhwb3J0cy5kZWxFdmVudCA9IGRlbEV2ZW50OwpleHBvcnRzLmV4cG9ydEV2ZW50ID0gZXhwb3J0RXZlbnQ7CmV4cG9ydHMuZ2V0RXZlbnQgPSBnZXRFdmVudDsKZXhwb3J0cy5nZXRFdmVudENoYXJ0ID0gZ2V0RXZlbnRDaGFydDsKZXhwb3J0cy5nZXRFdmVudFR5cGUgPSBnZXRFdmVudFR5cGU7CmV4cG9ydHMubGlzdEV2ZW50ID0gbGlzdEV2ZW50OwpleHBvcnRzLnVwZGF0ZUV2ZW50ID0gdXBkYXRlRXZlbnQ7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDmn6Xor6Lkuovku7bnrqHnkIYt5LqL5Lu25bqT5YiX6KGoCmZ1bmN0aW9uIGxpc3RFdmVudChxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2tmcXl3dGcvZXZlbnQvbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6Lkuovku7bnrqHnkIYt5LqL5Lu25bqT6K+m57uGCmZ1bmN0aW9uIGdldEV2ZW50KGlkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcva2ZxeXd0Zy9ldmVudC8nICsgaWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOafpeivouS6i+S7tuexu+Wei+S4i+aLiQpmdW5jdGlvbiBnZXRFdmVudFR5cGUoKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcva2ZxeXd0Zy9ldmVudC9ldmVudExhYmVsJywKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5paw5aKe5LqL5Lu2566h55CGLeS6i+S7tuW6kwpmdW5jdGlvbiBhZGRFdmVudChkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcva2ZxeXd0Zy9ldmVudCcsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS55LqL5Lu2566h55CGLeS6i+S7tuW6kwpmdW5jdGlvbiB1cGRhdGVFdmVudChkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcva2ZxeXd0Zy9ldmVudCcsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaTkuovku7bnrqHnkIYt5LqL5Lu25bqTCmZ1bmN0aW9uIGRlbEV2ZW50KGlkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcva2ZxeXd0Zy9ldmVudC8nICsgaWQsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0KCi8vIOWvvOWHuuS6i+S7tueuoeeQhi3kuovku7blupMKZnVuY3Rpb24gZXhwb3J0RXZlbnQocXVlcnkpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9rZnF5d3RnL2V2ZW50L2V4cG9ydCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6Lkuovku7blm77ooajmlbDmja4KZnVuY3Rpb24gZ2V0RXZlbnRDaGFydChxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2tmcXl3dGcvZXZlbnQvc3RhdGljJywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listEvent", "query", "request", "url", "method", "params", "getEvent", "id", "getEventType", "addEvent", "data", "updateEvent", "delEvent", "exportEvent", "getEventChart"], "sources": ["D:/project_about/git174/alznt-admin/src/api/screen/EventTask/event.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询事件管理-事件库列表\r\nexport function listEvent(query) {\r\n  return request({\r\n    url: '/kfqywtg/event/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询事件管理-事件库详细\r\nexport function getEvent(id) {\r\n  return request({\r\n    url: '/kfqywtg/event/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 查询事件类型下拉\r\nexport function getEventType() {\r\n  return request({\r\n    url: '/kfqywtg/event/eventLabel',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增事件管理-事件库\r\nexport function addEvent(data) {\r\n  return request({\r\n    url: '/kfqywtg/event',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改事件管理-事件库\r\nexport function updateEvent(data) {\r\n  return request({\r\n    url: '/kfqywtg/event',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除事件管理-事件库\r\nexport function delEvent(id) {\r\n  return request({\r\n    url: '/kfqywtg/event/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 导出事件管理-事件库\r\nexport function exportEvent(query) {\r\n  return request({\r\n    url: '/kfqywtg/event/export',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询事件图表数据\r\nexport function getEventChart(query) {\r\n  return request({\r\n    url: '/kfqywtg/event/static',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC/B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,QAAQA,CAACC,EAAE,EAAE;EAC3B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB,GAAGI,EAAE;IAC3BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,YAAYA,CAAA,EAAG;EAC7B,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,QAAQA,CAACC,IAAI,EAAE;EAC7B,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,WAAWA,CAACD,IAAI,EAAE;EAChC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,QAAQA,CAACL,EAAE,EAAE;EAC3B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB,GAAGI,EAAE;IAC3BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,WAAWA,CAACZ,KAAK,EAAE;EACjC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,aAAaA,CAACb,KAAK,EAAE;EACnC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}