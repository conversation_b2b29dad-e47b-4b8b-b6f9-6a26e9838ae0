{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\views\\login.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\views\\login.vue", "mtime": 1744789047407}, {"path": "D:\\project_about\\git174\\alznt-admin\\babel.config.js", "mtime": 1718238659557}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9wcm9qZWN0X2Fib3V0L2dpdDE3NC9hbHpudC1hZG1pbi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmNvbmNhdC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuaW5jbHVkZXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pbmNsdWRlcy5qcyIpOwp2YXIgX2xvZ2luID0gcmVxdWlyZSgiQC9hcGkvbG9naW4iKTsKdmFyIF9qc0Nvb2tpZSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgianMtY29va2llIikpOwp2YXIgX2pzZW5jcnlwdCA9IHJlcXVpcmUoIkAvdXRpbHMvanNlbmNyeXB0Iik7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAiTG9naW4iLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBzaG93TG9naW5Gb3JtOiBmYWxzZSwKICAgICAgc2NhblVybDogIiIuY29uY2F0KHByb2Nlc3MuZW52LlZVRV9BUFBfU0FPTUFfQ09ERSwgIi9vYXV0aDIvYXV0aC5odG0/cmVzcG9uc2VfdHlwZT1jb2RlJmNsaWVudF9pZD0iKS5jb25jYXQocHJvY2Vzcy5lbnYuVlVFX0FQUF9DTElFTlRfSUQsICImcmVkaXJlY3RfdXJpPSIpLmNvbmNhdChwcm9jZXNzLmVudi5WVUVfQVBQX1JFRElSRUNUX1VSSSwgIiZzY29wZT1nZXRfdXNlcl9pbmZvJmF1dGhUeXBlPVFSQ09ERSZlbWJlZE1vZGU9dHJ1ZSIpLAogICAgICBjb2RlVXJsOiAiIiwKICAgICAgY29va2llUGFzc3dvcmQ6ICIiLAogICAgICBsb2dpbkZvcm06IHsKICAgICAgICB1c2VybmFtZTogImFkbWluIiwKICAgICAgICBwYXNzd29yZDogImFkbWluMTIzIiwKICAgICAgICByZW1lbWJlck1lOiBmYWxzZSwKICAgICAgICBjb2RlOiAiIiwKICAgICAgICB1dWlkOiAiIgogICAgICB9LAogICAgICBsb2dpblJ1bGVzOiB7CiAgICAgICAgdXNlcm5hbWU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwKICAgICAgICAgIG1lc3NhZ2U6ICLnlKjmiLflkI3kuI3og73kuLrnqboiCiAgICAgICAgfV0sCiAgICAgICAgcGFzc3dvcmQ6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwKICAgICAgICAgIG1lc3NhZ2U6ICLlr4bnoIHkuI3og73kuLrnqboiCiAgICAgICAgfV0sCiAgICAgICAgY29kZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgdHJpZ2dlcjogImNoYW5nZSIsCiAgICAgICAgICBtZXNzYWdlOiAi6aqM6K+B56CB5LiN6IO95Li656m6IgogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICByZWRpcmVjdDogdW5kZWZpbmVkCiAgICB9OwogIH0sCiAgd2F0Y2g6IHsKICAgICRyb3V0ZTogewogICAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKHJvdXRlKSB7CiAgICAgICAgY29uc29sZS5sb2coInJvdXRlIiwgcm91dGUpOwogICAgICAgIHRoaXMuc2hvd0xvZ2luRm9ybSA9IHJvdXRlLmZ1bGxQYXRoLmluY2x1ZGVzKCJ1c2VyIik7CiAgICAgIH0sCiAgICAgIGltbWVkaWF0ZTogdHJ1ZQogICAgfQogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0Q29kZSgpOwogICAgdGhpcy5nZXRDb29raWUoKTsKICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigibWVzc2FnZSIsIHRoaXMubG9naW5aWkQpOwogIH0sCiAgYmVmb3JlRGVzdHJveTogZnVuY3Rpb24gYmVmb3JlRGVzdHJveSgpIHsKICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCJtZXNzYWdlIiwgdGhpcy5sb2dpblpaRCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDmtZnmlL/pkonnmbvlvZUKICAgIGxvZ2luWlpEOiBmdW5jdGlvbiBsb2dpblpaRChldmVudCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICB2YXIgY29kZSA9IGV2ZW50LmRhdGEgJiYgZXZlbnQuZGF0YS5jb2RlOwogICAgICBjb25zb2xlLmxvZygiY29kZSIsIGV2ZW50KTsKICAgICAgaWYgKGNvZGUpIHsKICAgICAgICBjb25zb2xlLmxvZygiY29kZTExMTEiLCBjb2RlKTsKICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgiTG9naW4iLCB7CiAgICAgICAgICB6emRDb2RlOiBjb2RlCiAgICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgICBfdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgICAgICBwYXRoOiBfdGhpcy5yZWRpcmVjdCB8fCAiLyIKICAgICAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHt9KTsKICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICBfdGhpcy5nZXRDb2RlKCk7CiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgICBnZXRDb2RlOiBmdW5jdGlvbiBnZXRDb2RlKCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgKDAsIF9sb2dpbi5nZXRDb2RlSW1nKSgpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzMi5jb2RlVXJsID0gImRhdGE6aW1hZ2UvZ2lmO2Jhc2U2NCwiICsgcmVzLmltZzsKICAgICAgICBfdGhpczIubG9naW5Gb3JtLnV1aWQgPSByZXMudXVpZDsKICAgICAgfSk7CiAgICB9LAogICAgZ2V0Q29va2llOiBmdW5jdGlvbiBnZXRDb29raWUoKSB7CiAgICAgIHZhciB1c2VybmFtZSA9IF9qc0Nvb2tpZS5kZWZhdWx0LmdldCgidXNlcm5hbWUiKTsKICAgICAgdmFyIHBhc3N3b3JkID0gX2pzQ29va2llLmRlZmF1bHQuZ2V0KCJwYXNzd29yZCIpOwogICAgICB2YXIgcmVtZW1iZXJNZSA9IF9qc0Nvb2tpZS5kZWZhdWx0LmdldCgicmVtZW1iZXJNZSIpOwogICAgICB0aGlzLmxvZ2luRm9ybSA9IHsKICAgICAgICB1c2VybmFtZTogdXNlcm5hbWUgPT09IHVuZGVmaW5lZCA/IHRoaXMubG9naW5Gb3JtLnVzZXJuYW1lIDogdXNlcm5hbWUsCiAgICAgICAgcGFzc3dvcmQ6IHBhc3N3b3JkID09PSB1bmRlZmluZWQgPyB0aGlzLmxvZ2luRm9ybS5wYXNzd29yZCA6ICgwLCBfanNlbmNyeXB0LmRlY3J5cHQpKHBhc3N3b3JkKSwKICAgICAgICByZW1lbWJlck1lOiByZW1lbWJlck1lID09PSB1bmRlZmluZWQgPyBmYWxzZSA6IEJvb2xlYW4ocmVtZW1iZXJNZSkKICAgICAgfTsKICAgIH0sCiAgICBoYW5kbGVMb2dpbjogZnVuY3Rpb24gaGFuZGxlTG9naW4oKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzLmxvZ2luRm9ybS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIF90aGlzMy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgIGlmIChfdGhpczMubG9naW5Gb3JtLnJlbWVtYmVyTWUpIHsKICAgICAgICAgICAgX2pzQ29va2llLmRlZmF1bHQuc2V0KCJ1c2VybmFtZSIsIF90aGlzMy5sb2dpbkZvcm0udXNlcm5hbWUsIHsKICAgICAgICAgICAgICBleHBpcmVzOiAzMAogICAgICAgICAgICB9KTsKICAgICAgICAgICAgX2pzQ29va2llLmRlZmF1bHQuc2V0KCJwYXNzd29yZCIsICgwLCBfanNlbmNyeXB0LmVuY3J5cHQpKF90aGlzMy5sb2dpbkZvcm0ucGFzc3dvcmQpLCB7CiAgICAgICAgICAgICAgZXhwaXJlczogMzAKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIF9qc0Nvb2tpZS5kZWZhdWx0LnNldCgicmVtZW1iZXJNZSIsIF90aGlzMy5sb2dpbkZvcm0ucmVtZW1iZXJNZSwgewogICAgICAgICAgICAgIGV4cGlyZXM6IDMwCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgX2pzQ29va2llLmRlZmF1bHQucmVtb3ZlKCJ1c2VybmFtZSIpOwogICAgICAgICAgICBfanNDb29raWUuZGVmYXVsdC5yZW1vdmUoInBhc3N3b3JkIik7CiAgICAgICAgICAgIF9qc0Nvb2tpZS5kZWZhdWx0LnJlbW92ZSgicmVtZW1iZXJNZSIpOwogICAgICAgICAgfQogICAgICAgICAgX3RoaXMzLmxvZ2luRm9ybS5wYXNzd29yZCA9ICgwLCBfanNlbmNyeXB0LmVuY3J5cHQpKF90aGlzMy5sb2dpbkZvcm0ucGFzc3dvcmQpOwogICAgICAgICAgX3RoaXMzLiRzdG9yZS5kaXNwYXRjaCgiTG9naW4iLCBfdGhpczMubG9naW5Gb3JtKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgX3RoaXMzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgICAgICAgcGF0aDogX3RoaXMzLnJlZGlyZWN0IHx8ICIvIgogICAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgIF90aGlzMy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgIF90aGlzMy5nZXRDb2RlKCk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_login", "require", "_js<PERSON><PERSON>ie", "_interopRequireDefault", "_jsencrypt", "name", "data", "showLoginForm", "scanUrl", "concat", "process", "env", "VUE_APP_SAOMA_CODE", "VUE_APP_CLIENT_ID", "VUE_APP_REDIRECT_URI", "codeUrl", "cookiePassword", "loginForm", "username", "password", "rememberMe", "code", "uuid", "loginRules", "required", "trigger", "message", "loading", "redirect", "undefined", "watch", "$route", "handler", "route", "console", "log", "fullPath", "includes", "immediate", "created", "getCode", "<PERSON><PERSON><PERSON><PERSON>", "mounted", "window", "addEventListener", "loginZZD", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "methods", "event", "_this", "$store", "dispatch", "zzdCode", "then", "$router", "push", "path", "catch", "_this2", "getCodeImg", "res", "img", "Cookies", "get", "decrypt", "Boolean", "handleLogin", "_this3", "$refs", "validate", "valid", "set", "expires", "encrypt", "remove"], "sources": ["src/views/login.vue"], "sourcesContent": ["<template>\r\n  <div class=\"login\">\r\n    <el-form\r\n      v-if=\"showLoginForm\"\r\n      ref=\"loginForm\"\r\n      :model=\"loginForm\"\r\n      :rules=\"loginRules\"\r\n      class=\"login-form\"\r\n    >\r\n      <h3 class=\"title\">开发区一网统管后台管理系统</h3>\r\n      <el-form-item prop=\"username\">\r\n        <el-input\r\n          v-model=\"loginForm.username\"\r\n          type=\"text\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"账号\"\r\n        >\r\n          <svg-icon\r\n            slot=\"prefix\"\r\n            icon-class=\"user\"\r\n            class=\"el-input__icon input-icon\"\r\n          />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"password\">\r\n        <el-input\r\n          v-model=\"loginForm.password\"\r\n          type=\"password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"密码\"\r\n          @keyup.enter.native=\"handleLogin\"\r\n        >\r\n          <svg-icon\r\n            slot=\"prefix\"\r\n            icon-class=\"password\"\r\n            class=\"el-input__icon input-icon\"\r\n          />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"code\">\r\n        <el-input\r\n          v-model=\"loginForm.code\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"验证码\"\r\n          style=\"width: 63%\"\r\n          @keyup.enter.native=\"handleLogin\"\r\n        >\r\n          <svg-icon\r\n            slot=\"prefix\"\r\n            icon-class=\"validCode\"\r\n            class=\"el-input__icon input-icon\"\r\n          />\r\n        </el-input>\r\n        <div class=\"login-code\">\r\n          <img :src=\"codeUrl\" @click=\"getCode\" class=\"login-code-img\" />\r\n        </div>\r\n      </el-form-item>\r\n      <el-checkbox\r\n        v-model=\"loginForm.rememberMe\"\r\n        style=\"margin: 0px 0px 25px 0px\"\r\n        >记住密码</el-checkbox\r\n      >\r\n      <el-form-item style=\"width: 100%\">\r\n        <el-button\r\n          :loading=\"loading\"\r\n          size=\"medium\"\r\n          type=\"primary\"\r\n          style=\"width: 100%\"\r\n          @click.native.prevent=\"handleLogin\"\r\n        >\r\n          <span v-if=\"!loading\">登 录</span>\r\n          <span v-else>登 录 中...</span>\r\n        </el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n    <div class=\"login_box\" v-else>\r\n      <h3 class=\"title\">开发区一网统管后台管理系统</h3>\r\n      <iframe\r\n        id=\"mainIframe\"\r\n        style=\"padding: 25px; padding-bottom: 5px\"\r\n        width=\"400px\"\r\n        height=\"350px\"\r\n        frameborder=\"0\"\r\n        scrolling=\"0\"\r\n        :src=\"scanUrl\"\r\n        sandbox=\"allow-scripts allow-top-navigation allow-same-origin\"\r\n      ></iframe>\r\n    </div>\r\n    <!--  底部  -->\r\n    <div class=\"el-login-footer\">\r\n      <span>Copyright © 2018-2021 ruoyi.vip All Rights Reserved.</span>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCodeImg } from \"@/api/login\";\r\nimport Cookies from \"js-cookie\";\r\nimport { encrypt, decrypt } from \"@/utils/jsencrypt\";\r\n\r\nexport default {\r\n  name: \"Login\",\r\n  data() {\r\n    return {\r\n      showLoginForm: false,\r\n      scanUrl: `${process.env.VUE_APP_SAOMA_CODE}/oauth2/auth.htm?response_type=code&client_id=${process.env.VUE_APP_CLIENT_ID}&redirect_uri=${process.env.VUE_APP_REDIRECT_URI}&scope=get_user_info&authType=QRCODE&embedMode=true`,\r\n      codeUrl: \"\",\r\n      cookiePassword: \"\",\r\n      loginForm: {\r\n        username: \"admin\",\r\n        password: \"admin123\",\r\n        rememberMe: false,\r\n        code: \"\",\r\n        uuid: \"\",\r\n      },\r\n      loginRules: {\r\n        username: [\r\n          { required: true, trigger: \"blur\", message: \"用户名不能为空\" },\r\n        ],\r\n        password: [\r\n          { required: true, trigger: \"blur\", message: \"密码不能为空\" },\r\n        ],\r\n        code: [\r\n          { required: true, trigger: \"change\", message: \"验证码不能为空\" },\r\n        ],\r\n      },\r\n      loading: false,\r\n      redirect: undefined,\r\n    };\r\n  },\r\n  watch: {\r\n    $route: {\r\n      handler: function (route) {\r\n        console.log(\"route\", route);\r\n        this.showLoginForm = route.fullPath.includes(\"user\");\r\n      },\r\n      immediate: true,\r\n    },\r\n  },\r\n  created() {\r\n    this.getCode();\r\n    this.getCookie();\r\n  },\r\n  mounted() {\r\n    window.addEventListener(\"message\", this.loginZZD);\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener(\"message\", this.loginZZD);\r\n  },\r\n  methods: {\r\n    // 浙政钉登录\r\n    loginZZD(event) {\r\n      const code = event.data && event.data.code;\r\n      console.log(\"code\", event);\r\n      if (code) {\r\n        console.log(\"code1111\", code);\r\n        this.$store\r\n          .dispatch(\"Login\", { zzdCode: code })\r\n          .then(() => {\r\n            this.$router.push({ path: this.redirect || \"/\" }).catch(() => {});\r\n          })\r\n          .catch(() => {\r\n            this.loading = false;\r\n            this.getCode();\r\n          });\r\n      }\r\n    },\r\n    getCode() {\r\n      getCodeImg().then((res) => {\r\n        this.codeUrl = \"data:image/gif;base64,\" + res.img;\r\n        this.loginForm.uuid = res.uuid;\r\n      });\r\n    },\r\n    getCookie() {\r\n      const username = Cookies.get(\"username\");\r\n      const password = Cookies.get(\"password\");\r\n      const rememberMe = Cookies.get(\"rememberMe\");\r\n      this.loginForm = {\r\n        username: username === undefined ? this.loginForm.username : username,\r\n        password:\r\n          password === undefined ? this.loginForm.password : decrypt(password),\r\n        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),\r\n      };\r\n    },\r\n    handleLogin() {\r\n      this.$refs.loginForm.validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          if (this.loginForm.rememberMe) {\r\n            Cookies.set(\"username\", this.loginForm.username, { expires: 30 });\r\n            Cookies.set(\"password\", encrypt(this.loginForm.password), {\r\n              expires: 30,\r\n            });\r\n            Cookies.set(\"rememberMe\", this.loginForm.rememberMe, {\r\n              expires: 30,\r\n            });\r\n          } else {\r\n            Cookies.remove(\"username\");\r\n            Cookies.remove(\"password\");\r\n            Cookies.remove(\"rememberMe\");\r\n          }\r\n          this.loginForm.password = encrypt(this.loginForm.password);\r\n          this.$store\r\n            .dispatch(\"Login\", this.loginForm)\r\n            .then(() => {\r\n              this.$router.push({ path: this.redirect || \"/\" }).catch(() => {});\r\n            })\r\n            .catch(() => {\r\n              this.loading = false;\r\n              this.getCode();\r\n            });\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\">\r\n.login {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  background-image: url(\"../assets/images/login-background.jpg\");\r\n  background-size: cover;\r\n}\r\n.title {\r\n  margin: 0px auto 30px auto;\r\n  text-align: center;\r\n  color: #707070;\r\n}\r\n\r\n.login-form {\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  width: 400px;\r\n  padding: 25px 25px 5px 25px;\r\n  .el-input {\r\n    height: 38px;\r\n    input {\r\n      height: 38px;\r\n    }\r\n  }\r\n  .input-icon {\r\n    height: 39px;\r\n    width: 14px;\r\n    margin-left: 2px;\r\n  }\r\n}\r\n.login-tip {\r\n  font-size: 13px;\r\n  text-align: center;\r\n  color: #bfbfbf;\r\n}\r\n.login-code {\r\n  width: 33%;\r\n  height: 38px;\r\n  float: right;\r\n  img {\r\n    cursor: pointer;\r\n    vertical-align: middle;\r\n  }\r\n}\r\n.el-login-footer {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  position: fixed;\r\n  bottom: 0;\r\n  width: 100%;\r\n  text-align: center;\r\n  color: #fff;\r\n  font-family: Arial;\r\n  font-size: 12px;\r\n  letter-spacing: 1px;\r\n}\r\n.login-code-img {\r\n  height: 38px;\r\n}\r\n.login_box {\r\n  background: #fff;\r\n  padding-top: 24px;\r\n  .title {\r\n    margin: 0px auto;\r\n    text-align: center;\r\n    color: #707070;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;AAgGA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,OAAA,KAAAC,MAAA,CAAAC,OAAA,CAAAC,GAAA,CAAAC,kBAAA,oDAAAH,MAAA,CAAAC,OAAA,CAAAC,GAAA,CAAAE,iBAAA,oBAAAJ,MAAA,CAAAC,OAAA,CAAAC,GAAA,CAAAG,oBAAA;MACAC,OAAA;MACAC,cAAA;MACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACAC,UAAA;QACAL,QAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,IAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,OAAA;MACAC,QAAA,EAAAC;IACA;EACA;EACAC,KAAA;IACAC,MAAA;MACAC,OAAA,WAAAA,QAAAC,KAAA;QACAC,OAAA,CAAAC,GAAA,UAAAF,KAAA;QACA,KAAA1B,aAAA,GAAA0B,KAAA,CAAAG,QAAA,CAAAC,QAAA;MACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACAC,MAAA,CAAAC,gBAAA,iBAAAC,QAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACAH,MAAA,CAAAI,mBAAA,iBAAAF,QAAA;EACA;EACAG,OAAA;IACA;IACAH,QAAA,WAAAA,SAAAI,KAAA;MAAA,IAAAC,KAAA;MACA,IAAA7B,IAAA,GAAA4B,KAAA,CAAA3C,IAAA,IAAA2C,KAAA,CAAA3C,IAAA,CAAAe,IAAA;MACAa,OAAA,CAAAC,GAAA,SAAAc,KAAA;MACA,IAAA5B,IAAA;QACAa,OAAA,CAAAC,GAAA,aAAAd,IAAA;QACA,KAAA8B,MAAA,CACAC,QAAA;UAAAC,OAAA,EAAAhC;QAAA,GACAiC,IAAA;UACAJ,KAAA,CAAAK,OAAA,CAAAC,IAAA;YAAAC,IAAA,EAAAP,KAAA,CAAAtB,QAAA;UAAA,GAAA8B,KAAA;QACA,GACAA,KAAA;UACAR,KAAA,CAAAvB,OAAA;UACAuB,KAAA,CAAAV,OAAA;QACA;MACA;IACA;IACAA,OAAA,WAAAA,QAAA;MAAA,IAAAmB,MAAA;MACA,IAAAC,iBAAA,IAAAN,IAAA,WAAAO,GAAA;QACAF,MAAA,CAAA5C,OAAA,8BAAA8C,GAAA,CAAAC,GAAA;QACAH,MAAA,CAAA1C,SAAA,CAAAK,IAAA,GAAAuC,GAAA,CAAAvC,IAAA;MACA;IACA;IACAmB,SAAA,WAAAA,UAAA;MACA,IAAAvB,QAAA,GAAA6C,iBAAA,CAAAC,GAAA;MACA,IAAA7C,QAAA,GAAA4C,iBAAA,CAAAC,GAAA;MACA,IAAA5C,UAAA,GAAA2C,iBAAA,CAAAC,GAAA;MACA,KAAA/C,SAAA;QACAC,QAAA,EAAAA,QAAA,KAAAW,SAAA,QAAAZ,SAAA,CAAAC,QAAA,GAAAA,QAAA;QACAC,QAAA,EACAA,QAAA,KAAAU,SAAA,QAAAZ,SAAA,CAAAE,QAAA,OAAA8C,kBAAA,EAAA9C,QAAA;QACAC,UAAA,EAAAA,UAAA,KAAAS,SAAA,WAAAqC,OAAA,CAAA9C,UAAA;MACA;IACA;IACA+C,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAApD,SAAA,CAAAqD,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAAzC,OAAA;UACA,IAAAyC,MAAA,CAAAnD,SAAA,CAAAG,UAAA;YACA2C,iBAAA,CAAAS,GAAA,aAAAJ,MAAA,CAAAnD,SAAA,CAAAC,QAAA;cAAAuD,OAAA;YAAA;YACAV,iBAAA,CAAAS,GAAA,iBAAAE,kBAAA,EAAAN,MAAA,CAAAnD,SAAA,CAAAE,QAAA;cACAsD,OAAA;YACA;YACAV,iBAAA,CAAAS,GAAA,eAAAJ,MAAA,CAAAnD,SAAA,CAAAG,UAAA;cACAqD,OAAA;YACA;UACA;YACAV,iBAAA,CAAAY,MAAA;YACAZ,iBAAA,CAAAY,MAAA;YACAZ,iBAAA,CAAAY,MAAA;UACA;UACAP,MAAA,CAAAnD,SAAA,CAAAE,QAAA,OAAAuD,kBAAA,EAAAN,MAAA,CAAAnD,SAAA,CAAAE,QAAA;UACAiD,MAAA,CAAAjB,MAAA,CACAC,QAAA,UAAAgB,MAAA,CAAAnD,SAAA,EACAqC,IAAA;YACAc,MAAA,CAAAb,OAAA,CAAAC,IAAA;cAAAC,IAAA,EAAAW,MAAA,CAAAxC,QAAA;YAAA,GAAA8B,KAAA;UACA,GACAA,KAAA;YACAU,MAAA,CAAAzC,OAAA;YACAyC,MAAA,CAAA5B,OAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}