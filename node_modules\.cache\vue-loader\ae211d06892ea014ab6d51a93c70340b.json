{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\layout\\components\\Sidebar\\SidebarItem.vue?vue&type=template&id=2d2bbdc2", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\layout\\components\\Sidebar\\SidebarItem.vue", "mtime": 1718070340312}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1758071062035}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}