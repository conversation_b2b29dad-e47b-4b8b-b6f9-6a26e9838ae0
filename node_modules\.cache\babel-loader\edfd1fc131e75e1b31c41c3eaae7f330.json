{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project_about\\git174\\alznt-admin\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\project_about\\git174\\alznt-admin\\src\\api\\screen\\warning\\OtherWarning.js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\api\\screen\\warning\\OtherWarning.js", "mtime": 1740448089610}, {"path": "D:\\project_about\\git174\\alznt-admin\\babel.config.js", "mtime": 1718238659557}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1758071059938}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9wcm9qZWN0X2Fib3V0L2dpdDE3NC9hbHpudC1hZG1pbi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZXhwb3J0WXd0Z1lqID0gZXhwb3J0WXd0Z1lqOwpleHBvcnRzLmdldFl3dGdZaiA9IGdldFl3dGdZajsKZXhwb3J0cy5saXN0WXd0Z1lqID0gbGlzdFl3dGdZajsKZXhwb3J0cy51cGRhdGVZd3RnWWogPSB1cGRhdGVZd3RnWWo7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDmn6Xor6Lorr7lpIfpooTorabliJfooagKZnVuY3Rpb24gbGlzdFl3dGdZaihxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2tmcXl3dGcveXd0Z1J5WWovbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6Lorr7lpIfpooTorabor6bnu4YKZnVuY3Rpb24gZ2V0WXd0Z1lqKGlkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcva2ZxeXd0Zy95d3RnUnlZai8nICsgaWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOS/ruaUueiuvuWkh+mihOitpgpmdW5jdGlvbiB1cGRhdGVZd3RnWWooZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2tmcXl3dGcveXd0Z1J5WWovZWRpdCcsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5a+85Ye66K6+5aSH6aKE6K2mCmZ1bmN0aW9uIGV4cG9ydFl3dGdZaihxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2tmcXl3dGcveXd0Z1J5WWovZXhwb3J0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listYwtgYj", "query", "request", "url", "method", "params", "getYwtgYj", "id", "updateYwtgYj", "data", "exportYwtgYj"], "sources": ["D:/project_about/git174/alznt-admin/src/api/screen/warning/OtherWarning.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询设备预警列表\r\nexport function listYwtgYj(query) {\r\n  return request({\r\n    url: '/kfqywtg/ywtgRyYj/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询设备预警详细\r\nexport function getYwtgYj(id) {\r\n  return request({\r\n    url: '/kfqywtg/ywtgRyYj/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 修改设备预警\r\nexport function updateYwtgYj(data) {\r\n  return request({\r\n    url: '/kfqywtg/ywtgRyYj/edit',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 导出设备预警\r\nexport function exportYwtgYj(query) {\r\n  return request({\r\n    url: '/kfqywtg/ywtgRyYj/export',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,SAASA,CAACC,EAAE,EAAE;EAC5B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,EAAE;IAC9BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,YAAYA,CAACC,IAAI,EAAE;EACjC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,YAAYA,CAACT,KAAK,EAAE;EAClC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}