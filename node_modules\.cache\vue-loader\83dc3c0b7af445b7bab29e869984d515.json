{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\layout\\components\\Sidebar\\Link.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\layout\\components\\Sidebar\\Link.vue", "mtime": 1718070340311}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBpc0V4dGVybmFsIH0gZnJvbSAnQC91dGlscy92YWxpZGF0ZScNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBwcm9wczogew0KICAgIHRvOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICByZXF1aXJlZDogdHJ1ZQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICBpc0V4dGVybmFsKCkgew0KICAgICAgcmV0dXJuIGlzRXh0ZXJuYWwodGhpcy50bykNCiAgICB9LA0KICAgIHR5cGUoKSB7DQogICAgICBpZiAodGhpcy5pc0V4dGVybmFsKSB7DQogICAgICAgIHJldHVybiAnYScNCiAgICAgIH0NCiAgICAgIHJldHVybiAncm91dGVyLWxpbmsnDQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgbGlua1Byb3BzKHRvKSB7DQogICAgICBpZiAodGhpcy5pc0V4dGVybmFsKSB7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgaHJlZjogdG8sDQogICAgICAgICAgdGFyZ2V0OiAnX2JsYW5rJywNCiAgICAgICAgICByZWw6ICdub29wZW5lcicNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgdG86IHRvDQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["Link.vue"], "names": [], "mappings": ";;;;;;;AAOA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Link.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<template>\r\n  <component :is=\"type\" v-bind=\"linkProps(to)\">\r\n    <slot />\r\n  </component>\r\n</template>\r\n\r\n<script>\r\nimport { isExternal } from '@/utils/validate'\r\n\r\nexport default {\r\n  props: {\r\n    to: {\r\n      type: String,\r\n      required: true\r\n    }\r\n  },\r\n  computed: {\r\n    isExternal() {\r\n      return isExternal(this.to)\r\n    },\r\n    type() {\r\n      if (this.isExternal) {\r\n        return 'a'\r\n      }\r\n      return 'router-link'\r\n    }\r\n  },\r\n  methods: {\r\n    linkProps(to) {\r\n      if (this.isExternal) {\r\n        return {\r\n          href: to,\r\n          target: '_blank',\r\n          rel: 'noopener'\r\n        }\r\n      }\r\n      return {\r\n        to: to\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}