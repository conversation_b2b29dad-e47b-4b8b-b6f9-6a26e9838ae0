{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project_about\\git174\\alznt-admin\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\project_about\\git174\\alznt-admin\\src\\api\\screen\\warning\\IoTEquipmentWarning.js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\api\\screen\\warning\\IoTEquipmentWarning.js", "mtime": 1740448089609}, {"path": "D:\\project_about\\git174\\alznt-admin\\babel.config.js", "mtime": 1718238659557}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1758071059938}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9wcm9qZWN0X2Fib3V0L2dpdDE3NC9hbHpudC1hZG1pbi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0RGV2aWNlID0gZ2V0RGV2aWNlOwpleHBvcnRzLmdldFBrTGlzdCA9IGdldFBrTGlzdDsKZXhwb3J0cy5saXN0WXd0Z1lqID0gbGlzdFl3dGdZajsKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Ci8vIOafpeivouiuvuWkh+mihOitpuWIl+ihqApmdW5jdGlvbiBsaXN0WXd0Z1lqKHF1ZXJ5KSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcva2ZxeXd0Zy95d3RnWWovbGlzdFdsJywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivouS6p+WTgeWIl+ihqApmdW5jdGlvbiBnZXRQa0xpc3QoKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcva2ZxeXd0Zy9way9saXN0JywKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5p+l6K+i6K6+5aSH6K+m57uG5L+h5oGvCmZ1bmN0aW9uIGdldERldmljZShpZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAiL2tmcXl3dGcvcGsvIi5jb25jYXQoaWQpLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listYwtgYj", "query", "request", "url", "method", "params", "getPkList", "getDevice", "id", "concat"], "sources": ["D:/project_about/git174/alznt-admin/src/api/screen/warning/IoTEquipmentWarning.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询设备预警列表\r\nexport function listYwtgYj(query) {\r\n  return request({\r\n    url: '/kfqywtg/ywtgYj/listWl',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询产品列表\r\nexport function getPkList() {\r\n  return request({\r\n    url: '/kfqywtg/pk/list',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 查询设备详细信息\r\nexport function getDevice(id) {\r\n  return request({\r\n    url: `/kfqywtg/pk/${id}`,\r\n    method: 'get'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,SAASA,CAAA,EAAG;EAC1B,OAAO,IAAAJ,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,SAASA,CAACC,EAAE,EAAE;EAC5B,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,iBAAAM,MAAA,CAAiBD,EAAE,CAAE;IACxBJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}