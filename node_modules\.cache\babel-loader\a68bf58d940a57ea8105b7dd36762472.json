{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\layout\\components\\Settings\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\layout\\components\\Settings\\index.vue", "mtime": 1718070340311}, {"path": "D:\\project_about\\git174\\alznt-admin\\babel.config.js", "mtime": 1718238659557}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ThemePicker", "_interopRequireDefault", "require", "components", "ThemePicker", "data", "computed", "theme", "$store", "state", "settings", "sideTheme", "fixedHeader", "get", "set", "val", "dispatch", "key", "value", "tagsView", "sidebarLogo", "methods", "themeChange", "handleTheme"], "sources": ["src/layout/components/Settings/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"drawer-container\">\r\n    <div>\r\n      <div class=\"setting-drawer-content\">\r\n        <div class=\"setting-drawer-title\">\r\n          <h3 class=\"drawer-title\">主题风格设置</h3>\r\n        </div>\r\n        <div class=\"setting-drawer-block-checbox\">\r\n          <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('theme-dark')\">\r\n            <img src=\"@/assets/images/dark.svg\" alt=\"dark\">\r\n            <div v-if=\"sideTheme === 'theme-dark'\" class=\"setting-drawer-block-checbox-selectIcon\" style=\"display: block;\">\r\n              <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\r\n                <svg viewBox=\"64 64 896 896\" data-icon=\"check\" width=\"1em\" height=\"1em\" :fill=\"theme\" aria-hidden=\"true\"\r\n                     focusable=\"false\" class=\"\">\r\n                  <path\r\n                    d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\"/>\r\n                </svg>\r\n              </i>\r\n            </div>\r\n          </div>\r\n          <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('theme-light')\">\r\n            <img src=\"@/assets/images/light.svg\" alt=\"light\">\r\n            <div v-if=\"sideTheme === 'theme-light'\" class=\"setting-drawer-block-checbox-selectIcon\" style=\"display: block;\">\r\n              <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\r\n                <svg viewBox=\"64 64 896 896\" data-icon=\"check\" width=\"1em\" height=\"1em\" :fill=\"theme\" aria-hidden=\"true\"\r\n                     focusable=\"false\" class=\"\">\r\n                  <path\r\n                    d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\"/>\r\n                </svg>\r\n              </i>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>主题颜色</span>\r\n          <theme-picker style=\"float: right;height: 26px;margin: -3px 8px 0 0;\" @change=\"themeChange\" />\r\n        </div>\r\n      </div>\r\n\r\n      <el-divider/>\r\n\r\n      <h3 class=\"drawer-title\">系统布局配置</h3>\r\n\r\n      <div class=\"drawer-item\">\r\n        <span>开启 Tags-Views</span>\r\n        <el-switch v-model=\"tagsView\" class=\"drawer-switch\" />\r\n      </div>\r\n\r\n      <div class=\"drawer-item\">\r\n        <span>固定 Header</span>\r\n        <el-switch v-model=\"fixedHeader\" class=\"drawer-switch\" />\r\n      </div>\r\n\r\n      <div class=\"drawer-item\">\r\n        <span>显示 Logo</span>\r\n        <el-switch v-model=\"sidebarLogo\" class=\"drawer-switch\" />\r\n      </div>\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ThemePicker from '@/components/ThemePicker'\r\n\r\nexport default {\r\n  components: { ThemePicker },\r\n  data() {\r\n    return {}\r\n  },\r\n  computed: {\r\n    theme() {\r\n      return this.$store.state.settings.theme\r\n    },\r\n    sideTheme() {\r\n      return this.$store.state.settings.sideTheme\r\n    },\r\n    fixedHeader: {\r\n      get() {\r\n        return this.$store.state.settings.fixedHeader\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'fixedHeader',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    tagsView: {\r\n      get() {\r\n        return this.$store.state.settings.tagsView\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'tagsView',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    sidebarLogo: {\r\n      get() {\r\n        return this.$store.state.settings.sidebarLogo\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'sidebarLogo',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n  },\r\n  methods: {\r\n    themeChange(val) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'theme',\r\n        value: val\r\n      })\r\n    },\r\n    handleTheme(val) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'sideTheme',\r\n        value: val\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .setting-drawer-content {\r\n    .setting-drawer-title {\r\n      margin-bottom: 12px;\r\n      color: rgba(0, 0, 0, .85);\r\n      font-size: 14px;\r\n      line-height: 22px;\r\n      font-weight: bold;\r\n    }\r\n\r\n    .setting-drawer-block-checbox {\r\n      display: flex;\r\n      justify-content: flex-start;\r\n      align-items: center;\r\n      margin-top: 10px;\r\n      margin-bottom: 20px;\r\n\r\n      .setting-drawer-block-checbox-item {\r\n        position: relative;\r\n        margin-right: 16px;\r\n        border-radius: 2px;\r\n        cursor: pointer;\r\n\r\n        img {\r\n          width: 48px;\r\n          height: 48px;\r\n        }\r\n\r\n        .setting-drawer-block-checbox-selectIcon {\r\n          position: absolute;\r\n          top: 0;\r\n          right: 0;\r\n          width: 100%;\r\n          height: 100%;\r\n          padding-top: 15px;\r\n          padding-left: 24px;\r\n          color: #1890ff;\r\n          font-weight: 700;\r\n          font-size: 14px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .drawer-container {\r\n    padding: 24px;\r\n    font-size: 14px;\r\n    line-height: 1.5;\r\n    word-wrap: break-word;\r\n\r\n    .drawer-title {\r\n      margin-bottom: 12px;\r\n      color: rgba(0, 0, 0, .85);\r\n      font-size: 14px;\r\n      line-height: 22px;\r\n    }\r\n\r\n    .drawer-item {\r\n      color: rgba(0, 0, 0, .65);\r\n      font-size: 14px;\r\n      padding: 12px 0;\r\n    }\r\n\r\n    .drawer-switch {\r\n      float: right\r\n    }\r\n  }\r\n</style>\r\n"], "mappings": ";;;;;;;AAgEA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,UAAA;IAAAC,WAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;EACA;EACAC,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,KAAA;IACA;IACAI,SAAA,WAAAA,UAAA;MACA,YAAAH,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,SAAA;IACA;IACAC,WAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAL,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAE,WAAA;MACA;MACAE,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAP,MAAA,CAAAQ,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;IACAI,QAAA;MACAN,GAAA,WAAAA,IAAA;QACA,YAAAL,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAS,QAAA;MACA;MACAL,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAP,MAAA,CAAAQ,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;IACAK,WAAA;MACAP,GAAA,WAAAA,IAAA;QACA,YAAAL,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAU,WAAA;MACA;MACAN,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAP,MAAA,CAAAQ,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;EACA;EACAM,OAAA;IACAC,WAAA,WAAAA,YAAAP,GAAA;MACA,KAAAP,MAAA,CAAAQ,QAAA;QACAC,GAAA;QACAC,KAAA,EAAAH;MACA;IACA;IACAQ,WAAA,WAAAA,YAAAR,GAAA;MACA,KAAAP,MAAA,CAAAQ,QAAA;QACAC,GAAA;QACAC,KAAA,EAAAH;MACA;IACA;EACA;AACA", "ignoreList": []}]}