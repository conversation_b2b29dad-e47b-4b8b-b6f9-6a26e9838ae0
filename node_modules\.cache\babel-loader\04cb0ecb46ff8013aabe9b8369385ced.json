{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\views\\components\\icons\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\views\\components\\icons\\index.vue", "mtime": 1748224634823}, {"path": "D:\\project_about\\git174\\alznt-admin\\babel.config.js", "mtime": 1718238659557}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9wcm9qZWN0X2Fib3V0L2dpdDE3NC9hbHpudC1hZG1pbi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9zdmdJY29ucyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9zdmctaWNvbnMiKSk7CnZhciBfZWxlbWVudEljb25zID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL2VsZW1lbnQtaWNvbnMiKSk7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnSWNvbnMnLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBzdmdJY29uczogX3N2Z0ljb25zLmRlZmF1bHQsCiAgICAgIGVsZW1lbnRJY29uczogX2VsZW1lbnRJY29ucy5kZWZhdWx0CiAgICB9OwogIH0sCiAgbWV0aG9kczogewogICAgZ2VuZXJhdGVJY29uQ29kZTogZnVuY3Rpb24gZ2VuZXJhdGVJY29uQ29kZShzeW1ib2wpIHsKICAgICAgcmV0dXJuICI8c3ZnLWljb24gaWNvbi1jbGFzcz1cIiIuY29uY2F0KHN5bWJvbCwgIlwiIC8+Iik7CiAgICB9LAogICAgZ2VuZXJhdGVFbGVtZW50SWNvbkNvZGU6IGZ1bmN0aW9uIGdlbmVyYXRlRWxlbWVudEljb25Db2RlKHN5bWJvbCkgewogICAgICByZXR1cm4gIjxpIGNsYXNzPVwiZWwtaWNvbi0iLmNvbmNhdChzeW1ib2wsICJcIiAvPiIpOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_svgIcons", "_interopRequireDefault", "require", "_elementIcons", "name", "data", "svgIcons", "elementIcons", "methods", "generateIconCode", "symbol", "concat", "generateElementIconCode"], "sources": ["src/views/components/icons/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"icons-container\">\r\n    <aside>\r\n      <a href=\"#\" target=\"_blank\">Add and use\r\n      </a>\r\n    </aside>\r\n    <el-tabs type=\"border-card\">\r\n      <el-tab-pane label=\"Icons\">\r\n        <div v-for=\"item of svgIcons\" :key=\"item\">\r\n          <el-tooltip placement=\"top\">\r\n            <div slot=\"content\">\r\n              {{ generateIconCode(item) }}\r\n            </div>\r\n            <div class=\"icon-item\">\r\n              <svg-icon :icon-class=\"item\" class-name=\"disabled\" />\r\n              <span>{{ item }}</span>\r\n            </div>\r\n          </el-tooltip>\r\n        </div>\r\n      </el-tab-pane>\r\n      <el-tab-pane label=\"Element-UI Icons\">\r\n        <div v-for=\"item of elementIcons\" :key=\"item\">\r\n          <el-tooltip placement=\"top\">\r\n            <div slot=\"content\">\r\n              {{ generateElementIconCode(item) }}\r\n            </div>\r\n            <div class=\"icon-item\">\r\n              <i :class=\"'el-icon-' + item\" />\r\n              <span>{{ item }}</span>\r\n            </div>\r\n          </el-tooltip>\r\n        </div>\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport svgIcons from './svg-icons'\r\nimport elementIcons from './element-icons'\r\n\r\nexport default {\r\n  name: 'Icons',\r\n  data() {\r\n    return {\r\n      svgIcons,\r\n      elementIcons\r\n    }\r\n  },\r\n  methods: {\r\n    generateIconCode(symbol) {\r\n      return `<svg-icon icon-class=\"${symbol}\" />`\r\n    },\r\n    generateElementIconCode(symbol) {\r\n      return `<i class=\"el-icon-${symbol}\" />`\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.icons-container {\r\n  margin: 10px 20px 0;\r\n  overflow: hidden;\r\n\r\n  .icon-item {\r\n    margin: 20px;\r\n    height: 85px;\r\n    text-align: center;\r\n    width: 100px;\r\n    float: left;\r\n    font-size: 30px;\r\n    color: #24292e;\r\n    cursor: pointer;\r\n  }\r\n\r\n  span {\r\n    display: block;\r\n    font-size: 16px;\r\n    margin-top: 10px;\r\n  }\r\n\r\n  .disabled {\r\n    pointer-events: none;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAsCA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAF,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA,EAAAA,iBAAA;MACAC,YAAA,EAAAA;IACA;EACA;EACAC,OAAA;IACAC,gBAAA,WAAAA,iBAAAC,MAAA;MACA,iCAAAC,MAAA,CAAAD,MAAA;IACA;IACAE,uBAAA,WAAAA,wBAAAF,MAAA;MACA,6BAAAC,MAAA,CAAAD,MAAA;IACA;EACA;AACA", "ignoreList": []}]}