@use '../../helpers/baseMixin.scss';

.vxe-table-export--panel-column > ul {
  list-style-type: none;
  overflow: auto;
  margin: 0;
  padding: 0;
  outline: 0;
  user-select: none;
  & > li {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
  }
}

.vxe-table-export--panel {
  .vxe-table-export--panel-table {
    width: 100%;
    border: 0;
    table-layout: fixed;
    tr {
      td {
        padding: 0 10px;
        &:nth-child(1) {
          text-align: right;
          width: 30%;
          font-weight: 700;
          padding: 8px 10px;
        }
        &:nth-child(2) {
          width: 70%;
        }
        & > .vxe-input,
        & > .vxe-select {
          width: 80%;
        }
        & > .vxe-table-export--panel-option-row {
          padding: 0.25em 0;
        }
      }
    }
  }
  .vxe-table-export--panel-column {
    width: 80%;
    border: 1px solid var(--vxe-ui-input-border-color);
    margin: 3px 0;
    border-radius: var(--vxe-ui-border-radius);
    user-select: none;
    & > ul {
      & > li {
        padding: 0.2em 1em 0.2em 1em;
        @for $i from 2 through 8 {
          $interval: $i - 1 + 0.2;
          &.level--#{$i}{
            padding-left: #{$interval + 2.3}em;
            .vxe-checkbox--icon {
              left: #{$interval + 0.6}em;
            }
          }
        }
      }
    }
    .vxe-table-export--panel-column-header {
      padding: 0.1em 0;
      background-color: var(--vxe-ui-table-header-background-color);
      font-weight: 700;
      border-bottom: 1px solid var(--vxe-ui-table-border-color);
    }
    .vxe-table-export--panel-column-body {
      padding: 0.2em 0;
      min-height: 10em;
      max-height: 17.6em;
    }
  }
  .vxe-table-export--panel-btns {
    text-align: right;
    padding: 0.8em 0.25em 0.25em 0.25em;
  }
}

.vxe-table-export--panel {
  .vxe-table-export--selected--file {
    padding-right: 40px;
    position: relative;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    user-select: none;
    & > i {
      display: none;
      position: absolute;
      top: 50%;
      right: 15px;
      transform: translateY(-50%);
      font-size: 16px;
      cursor: pointer;
    }
    &:hover {
      color: var(--vxe-ui-font-primary-color);
      & > i {
        display: block;
      }
    }
  }
  .vxe-table-export--select--file {
    width: 80%;
    border: 1px dashed var(--vxe-ui-input-border-color);
    padding: 6px 34px;
    outline: 0;
    border-radius: var(--vxe-ui-border-radius);
    background-color: var(--vxe-ui-layout-background-color);
    color: inherit;
    user-select: none;
    cursor: pointer;
    &:focus {
      border-color: var(--vxe-ui-font-primary-color);
      box-shadow: 0 0 0.25em 0 var(--vxe-ui-font-primary-color);
    }
    &:hover {
      color: var(--vxe-ui-font-primary-color);
      border-color: var(--vxe-ui-font-primary-color);
    }
  }
}

.vxe-table-export--panel-column-option {
  @include baseMixin.createCheckboxIcon();
}
