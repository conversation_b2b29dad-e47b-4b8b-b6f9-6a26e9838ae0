@use "sass:map";
@use "sass:list";
@use './old-icon.scss';

@font-face {
  font-family: "vxetableiconfont";
  src: 
       url('data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAABAIAAsAAAAAIbgAAA+5AAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHFQGYACIOgqrQKNCATYCJAOBFAtMAAQgBYVHB4QlG6kcRQaGjQMBoG9hsv/rA13AEP3R2iPU2QJGwHFWIOKNtTIrVuzFJ8JEBv0CV71CpLWJr5Ftoeg/7wnGERxN07wfQxNbKv5aM9bI06GUPED74d7dv/+nImmoZteEVxJbbEjINKJYglA2RZu25CC+d0D8EIs5xO6IYRFvCyFVKESqQmqkeSGpKTX/S0WZLzUhFTMqIgzqVLllqWU77oD52jkWtDwwDNmP9EqvLQdGEBgBBFDGllrbQRsE5mT9C6UsWQtoz2EcQqFq54BjEWgfA37v8n+F0lo052R/kc/SfPXZ5GY3n9mZMJm8JhwO4XEKYzCW/1szbfYAwHXfvS3esVAofIWvMDOzyTWT+Qc/h3tEbY43xdyVgJ4HlJsSZsvsWmFJVcga2QpTrSuUreocxXh4mKxlK0D8NBib5MGqMYoJRsR/P4uAcrO2xZ2Dx8+rvnf52QPtktfOAWfmcO/2zctKj5VSQGxQWlZEvWESduXEd/EH3hvfP/w1H3yJ8szrvk7fOHDNBzft9hC+byX76m12C+DCETkyTFasKj4P7gjPZBafNES57HcqhcPY1Cvp/EHTLkklPYOBhpig1Ux2ez1/ijaSspJ6ZOrE049z9Nm7zVvnK/0rbs/jIWFxBkuv/Hf77S6f166CzXdxtqWD/ofvng7tanUrEyXlcjV6VQzUf+dlmrXq1KJSlTolerSp16BRk1J9+nWpNmCQUCAzVkL4gL5BftBYOh38wB0MSAKqBVQCdQP0QGUAA1AEDARKgMFA5YAhQDlgAlANoBWoFzATqAJgBwqAvUAF4DnSyfiJomZADFArQAnUCSgBagH0AKoEmICqACeA6gAfkU4Jv46iHsAroDbAa6B6wBugBsBboEbAO6TTxO88VArYDdQHcCKdfv6UptPF3xXpVPN/JhoAzMIIg2BIY9HPc4PiH6VfvJrCKNOLJKgQZYxp1rcWJcJkpYGI5EkLMiWiiAqBDWsNgY2NnbWJ1uGVMK7U09PZqx6VVlNDQ2tTzaSmMqTMbGqqGlrT2Di1sagBrweBXFdnNVUGdqVvrG3qrOooi69bVqVvS6NaJxsmP60vBC33FChxMQVwUkpBilWuHqBWBZIVLml00a7OvHXhmx9H/PXu+Mf0/jfah8oXXWbiLydZNer1CDsXryYf8WORl6bjaZlNWq4TpnLK9t1IFUQiaJJ5gyigqXKANbu92Whyz4X5of3+Ur9IqSjiYQizxuK9UvXQmePZHbGshzXrdhmcapPr0hnb93TVX0fvRCPgcudx02l1lsJPC6BRv5Qs4D8Wz6ECPLVgQK78oHgFO8QdsL5EVaUyyflgSm0EUq+NbEsn0a6GL5hyLmCN0BPPvbyWsvvidL6vhS3QUdHs7jRaLF8QEvWBenoxcXgtoWlDxIoWgUvpncdm8VUrAoU1TN35ax9CR7nfCFkfOkqDSbo4PajqTt2pfW1qQCaOa1FQ1rEolbwC/+1ZELOQFu/pi1nmtlO+AH3L0c7TCJHfjsOUVcmrsgW2X7h86dWnD1OdcJ0ft8hhTQcHeTj6h4UdRvRlzSxgzfbBDeZk8o2CXaDtW8/g9UfyvXeloloCfGpSnok42TA/4VA7P/7jDMpikbekXhEr515VLqd7Dp4iazVajFZjHNVrOtSDrN+cDLSeA/QwE7J2nIb4j1D15DP/1SLRXSBgSilokV/uIFnvfkWt6vv9LvJ+79tOyohPguaqpm1M8loDgGj0hAgBWAsQsP26KZH6Oiv8PaJZW+22xiy+p2ihdBnx6KTF6WBRcfNEi/fMHPH0FZCsV2UuccIJeTC/Xia4NhMccA5BXEVtD8GeecLmFMR+QDzXU2W7iwJidDaTBJ0WcLhfICgN0JLM82xJYkcWa3KiyDXZiTU5DjzVMmC0FlipNBBLpRMvtgr/Y/C/X/w/vco/GNinOlV8Yuh/JejM0jzq9kKZopXf7s1xGm6y03nnC9y+etBne7JmuxKwfIBU7sFweIL/kRJW7Jd/OJWFfNO+V1a7jHjKmmnQcbcjHnMdOodQ6GAhLg4gXXwEdKduhtofXJyZnB7VXeMmi24c7ORV5Pvkkxv85lP1/PZygAO69Xul/dKVDrnyefm6pwna1X+gWMVGUsYQhR7QTCuXbXHbti7boqZcw35WtYJVBlmlOR1f7jjkuatBch0ahrMjcdIMg9vZlONTPSIOi2PY4xksNpybEgd04OORKHWCECgfmPjFWOyxz2tYfN74tptXeIDHjl2Ir+RMhhDHJxGqWA47bwqVGhEvS1Cqg6uA1SK5RWQ7c4TE8NX1cDhZh2qcnptONcwHwzVt26zd+fUhxgA/a5GXa4lDL62m+hH8WK1UViXacqNIivZcDWOuVzqK2Gj6yGUQk/Xlc+CQMGlsbiYbCJOJaCCbR5nkxpxsM9lgGUmacwyGHDM50tJAmrNzjHITQB4pWia3fDwRyap4yWRJ/Cq1xBrakj85IjXGBy3I8KEwBPIZej0lbBA49XXRSgl12CLwqHNiOiFVVwdJ6AlyLnhvxKTYZmYzbrZA3dDUYPu8sVWGbkVlmEIllv6F3tF+PnIkqmjDcD6H3/fzncu/+81RzrH033yVDkzGe+pQFU8l/BmmbhYq68dEZ2FKWM7lUxmCnK15mQEBw6UNo1p55Qbp8ICATEzKk2JuraGN25bCTZbS3xCQ1bZePSEbF8OejhHjorF7QRHOgr0MWDj73aTORq6IN+x7pzvv/ZwuVvgMkR4O83/tP/r9vrWAwRDKstci+iVATouggq6ll9C70e9tuRNZ6q33051GFid1gzy+FAtjAuy6s9l3iM79uU0nqX2S/c74M81vwHdBNq37i00nqn3xtD++HD8ACvv3Tu6RmFdT0CXuLF6P91VUqioNsUNTxmIj0V4aY1HvB9gYdKSmd7FhSEpXMWNHLuLrpSRtHpGys1J9Gtamd0t/MamZRzoTQ7Rpj3K/+v2PUhkyO1/J+y8jo1E6IiAAZB6agkpRLYOlHbRNi0q5pFX0RtiBGFGHkOKTvGSL/gkv7GZj+v0jmqPNz5e53WMGDZIWKNTbyRy7UCW055AaFhAiBZYmuZkkzfKmDyKuMJPfVX+gvWM95vPYt16l2bMplJ05DHcjhpmSEY+l4L+pdBRfshuPHR0rGZIQX1wSr5M+viRROOSIOP2YPEtjCEFYGkN8IoRoXLSnzWeOvKkzQtLTQ/TniRlWfZ7WN/IS0rXrZMtBtZYJNj+2V8IhHhaYA27xTnuEeZ70D541G1PztfxufHUxfO27yNcq4hMZ9lm5cV4ne/ykLF95WMNbnc7j2bOjkgryQYZEsVqQe4mfUy/lClbzNwXBldfvPn+uqspqDQysqgR0E9Eczu+TiGTxQbC6qYzkpMj7zxogHzCLlHOSTx15KG/U9T/o3kPluRPJAeS6Z6qe5CHJvlKtHHiSjpkIQ3a2gTC9EAE5uzYRLzxuPl1jG2vTp5UlnYhNol3Xpp+lvgHZ5G4zMVd78fjG0DFF49tdIGPruQljXBwT68Gp2eOLNoy37ePLo3h0KNh6c3xRUImgAWzaMdiVADJqte64f9/uw3EWHuVLJ62NI8bt8L1l4RFw2vhHM5obt4O4a9uhiLMfhYpwEYoPFornbpuSugNl42wUqRw23M4u6dHe21EWRySjbA4Lldp2vTB1Si7wAEYg/WBeXawiOloRa5K7UETHckcNKolpiKljPgFiNX0hKzHxaETzcB9mx+LFTmYsfGTl44E42McQ9fkifAhpOA1uUihGS2y6nW+n6MuQ3sIvEMAAntSVwLtK2/XrtghWhO3w4U7XMqRFjL1zxwa6vpbQNLSAhq7/SYyhGlrpQAlTA+o0/NdVQsjbG9IuZqlp9SOJWRND//YAkyYNHqzR7HqRuhJIU9yGABpPlZaa8b4R1NXj8+bhZrOuL15/i3pvhRn/MWELp0XY3uhCu4R0DdGGTpwYqqXjdgnVTZygDb1KiC70zV4DCGRFsAKpQHZeyASutKBMU7QpM1rb0p6LuXxZ+Uvzs88KfBCFMOR0CLk6NcP8ySxnJYKqoIOhY1xX2hxQxQHd+Ph1QonRsFcb1j+BMq4MPl3/KpsgnbPo0UM71/7wIcWRHATkYS8HvwBaKNf4YIYx5O7e83TM2dJqZrPNrS2nmeip1zVGRqHLRS2ABGvaPBj8ZuxYi2VPFVj/tel2/37xbME+qpPIQirjEyqQEas7qVvv2XAInDeNRcAFLspVaGTUvD6FMk+3trDNZnZLqxOjn997N4RhDB5PuSywgO+YUODMTvcGhD2SD6jGAnHsEGUd1x1P0+6TboJ1vY64PoF/Y/rtCUy/v9VJrR5RiSTEIxVZBNW5T8Ae4g/rrisL1K75Pu307sAOjA4xXQF/wDV6B62Y5Imwb3Sekw4k05D3H0GU8sdsFZIg7zuEKOTD+5E8+fQVpFy+fBBJ7HWNRGRRYs8hlfLRWUgQP5hEKiC14wEkn/aeuI1kFSEOZykfdyLE113cjCQDAABS79WNTOR12esJhf/BEjBYEbsL4fIfWZXmNEEomuSP9RxO93pODkT+9RrQxN5bexEmL442rcTgh6tWroL1P84XAp1uR1AfTP75n/mif0wf8K35uSft+/uS5ncpFf+bI/w3ubM/HbZ/sC3i3slQki0FW9sQ/LdArWDakF+QK6L8vQH/S26p968ZpQ2RmcZrOetBiUoMKJlab6UUuFJyFY4qhVrflHITcPMKDX6rFtLRGPZHUYJef5Wo2m8l0+u/JUWGFiXXHFZ4IQs8oZS7GBbfKgyHz8rhCIbkxp0rUThrXltvYWEk8Mf7GVOnoKJF4lt+h7Yi7E6HE/HAJzA0j6u09XRmll1WWdzH5/2BSFxVOUe0YTarq+PjLDM9RFkO3lEQGEic0U7LW0SCZb1Z24LhCkZkLTKIfz5ok37Yu0AjLhXl32mvagp/6pxTg5ME0ifBiRbZEN7usZMzl8kc40r7F85HogG9e4VT5ec7B5EZzAlc1coxWVWOKobiS5cZKozrxb7ufihE4aa7Gx4OqZzmP4Ou+lKnXoNGTZq1+Nn59rXr0KlLtx69+vQbMGjIcHv4JKQivnOcEX8g+VyInrh2NcpW9iouRZKGq5NKr4Wny+tAHe1KSMm3Pl5sm1DThKhdYfFJpV5EKvbwZ/D5rAj7WDQSMoFbedoOK9/nrPnMUrWwPSrFQoefdSv2ZrL4KNQXvlP5C16PQWE+yYav2o293jD7tUyzBflEyHY9d6IWFeCrS+EydrPH/WYPu5w7M+hqejqSkK60+5wMvRFUZfN8qMuKimA3kobJz4ET4UUm+sIJBk2/KljT6y/3Stru5LKtEauGKlrhOg93bTVlVb7ijS/nwQEA') format('woff2');
}

@keyframes rollCircle {
  0% { 
    transform: rotate(0deg);
  }
  100% { 
    transform: rotate(360deg);
  }
}

$btnThemeList: (
  (
    name: "primary",
    textColor: var(--vxe-ui-font-primary-color),
  ),
  (
    name: "success",
    textColor: var(--vxe-ui-status-success-color),
  ),
  (
    name: "info",
    textColor: var(--vxe-ui-status-info-color),
  ),
  (
    name: "warning",
    textColor: var(--vxe-ui-status-warning-color),
  ),
  (
    name: "danger",
    textColor: var(--vxe-ui-status-danger-color),
  ),
  (
    name: "error",
    textColor: var(--vxe-ui-status-error-color),
  )
);

[class*="vxe-table-icon-"] {
  font-family: "vxetableiconfont" !important;
  font-style: normal;
  font-weight: 400;
  font-size: 1.1em;
  line-height: 1em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  &.animat,
  &.roll {
    display: inline-block;
  }
  &.animat {
    transition: transform 0.25s ease-in-out;
  }
  &.rotate45 {
    transform: rotate(45deg);
  }
  &.rotate90 {
    transform: rotate(90deg);
  }
  &.rotate180 {
    transform: rotate(180deg);
  }
  &.roll {
    animation: rollCircle 1s infinite linear;
  }
  @for $index from 0 to list.length($btnThemeList) {
    $item: list.nth($btnThemeList, $index + 1);
    &.theme--#{map.get($item, name)} {
      color: map.get($item, textColor);
    }
  }
}

.vxe-table-icon-radio-unchecked-fill:before {
  content: "\e699";
}

.vxe-table-icon-checkbox-unchecked-fill:before {
  content: "\e660";
}

.vxe-table-icon-close:before {
  content: "\e6e9";
}

.vxe-table-icon-grouping:before {
  content: "\e66c";
}

.vxe-table-icon-values:before {
  content: "\e66f";
}

.vxe-table-icon-add-sub:before {
  content: "\e6bc";
}

.vxe-table-icon-swap:before {
  content: "\e7f3";
}

.vxe-table-icon-sort:before {
  content: "\e93e";
}

.vxe-table-icon-no-drop:before {
  content: "\e658";
}

.vxe-table-icon-edit:before {
  content: "\e66e";
}

.vxe-table-icon-question-circle-fill:before {
  content: "\e690";
}

.vxe-table-icon-radio-checked:before {
  content: "\e75b";
}

.vxe-table-icon-radio-checked-fill:before {
  content: "\e763";
}

.vxe-table-icon-print:before {
  content: "\eba0";
}

.vxe-table-icon-checkbox-checked-fill:before {
  content: "\e67d";
}

.vxe-table-icon-custom-column:before {
  content: "\e62d";
}

.vxe-table-icon-radio-unchecked:before {
  content: "\e7c9";
}

.vxe-table-icon-caret-down:before {
  content: "\e8ed";
}

.vxe-table-icon-caret-up:before {
  content: "\e8ee";
}

.vxe-table-icon-caret-right:before {
  content: "\e8ef";
}

.vxe-table-icon-caret-left:before {
  content: "\e8f0";
}

.vxe-table-icon-fullscreen:before {
  content: "\e70e";
}

.vxe-table-icon-minimize:before {
  content: "\e749";
}

.vxe-table-icon-checkbox-unchecked:before {
  content: "\e727";
}

.vxe-table-icon-funnel:before {
  content: "\e8ec";
}

.vxe-table-icon-download:before {
  content: "\e61a";
}

.vxe-table-icon-spinner:before {
  content: "\e601";
}

.vxe-table-icon-arrow-right:before {
  content: "\e743";
}

.vxe-table-icon-repeat:before {
  content: "\ea4a";
}

.vxe-table-icon-drag-handle:before {
  content: "\e64e";
}

.vxe-table-icon-checkbox-indeterminate-fill:before {
  content: "\e8c4";
}

.vxe-table-icon-upload:before {
  content: "\e683";
}

.vxe-table-icon-fixed-left-fill:before {
  content: "\e9b9";
}

.vxe-table-icon-fixed-left:before {
  content: "\e9ba";
}

.vxe-table-icon-fixed-right-fill:before {
  content: "\f290";
}

.vxe-table-icon-fixed-right:before {
  content: "\f291";
}

