{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\Pagination\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\Pagination\\index.vue", "mtime": 1718070340300}, {"path": "D:\\project_about\\git174\\alznt-admin\\babel.config.js", "mtime": 1718238659557}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5udW1iZXIuY29uc3RydWN0b3IuanMiKTsKdmFyIF9zY3JvbGxUbyA9IHJlcXVpcmUoIkAvdXRpbHMvc2Nyb2xsLXRvIik7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdDIgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgbmFtZTogJ1BhZ2luYXRpb24nLAogIHByb3BzOiB7CiAgICB0b3RhbDogewogICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgdHlwZTogTnVtYmVyCiAgICB9LAogICAgcGFnZTogewogICAgICB0eXBlOiBOdW1iZXIsCiAgICAgIGRlZmF1bHQ6IDEKICAgIH0sCiAgICBsaW1pdDogewogICAgICB0eXBlOiBOdW1iZXIsCiAgICAgIGRlZmF1bHQ6IDIwCiAgICB9LAogICAgcGFnZVNpemVzOiB7CiAgICAgIHR5cGU6IEFycmF5LAogICAgICBkZWZhdWx0OiBmdW5jdGlvbiBfZGVmYXVsdCgpIHsKICAgICAgICByZXR1cm4gWzEwLCAyMCwgMzAsIDUwXTsKICAgICAgfQogICAgfSwKICAgIGxheW91dDogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICd0b3RhbCwgc2l6ZXMsIHByZXYsIHBhZ2VyLCBuZXh0LCBqdW1wZXInCiAgICB9LAogICAgYmFja2dyb3VuZDogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiB0cnVlCiAgICB9LAogICAgYXV0b1Njcm9sbDogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiB0cnVlCiAgICB9LAogICAgaGlkZGVuOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlCiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgY3VycmVudFBhZ2U6IHsKICAgICAgZ2V0OiBmdW5jdGlvbiBnZXQoKSB7CiAgICAgICAgcmV0dXJuIHRoaXMucGFnZTsKICAgICAgfSwKICAgICAgc2V0OiBmdW5jdGlvbiBzZXQodmFsKSB7CiAgICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOnBhZ2UnLCB2YWwpOwogICAgICB9CiAgICB9LAogICAgcGFnZVNpemU6IHsKICAgICAgZ2V0OiBmdW5jdGlvbiBnZXQoKSB7CiAgICAgICAgcmV0dXJuIHRoaXMubGltaXQ7CiAgICAgIH0sCiAgICAgIHNldDogZnVuY3Rpb24gc2V0KHZhbCkgewogICAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTpsaW1pdCcsIHZhbCk7CiAgICAgIH0KICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGhhbmRsZVNpemVDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVNpemVDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMuJGVtaXQoJ3BhZ2luYXRpb24nLCB7CiAgICAgICAgcGFnZTogdGhpcy5jdXJyZW50UGFnZSwKICAgICAgICBsaW1pdDogdmFsCiAgICAgIH0pOwogICAgICBpZiAodGhpcy5hdXRvU2Nyb2xsKSB7CiAgICAgICAgKDAsIF9zY3JvbGxUby5zY3JvbGxUbykoMCwgODAwKTsKICAgICAgfQogICAgfSwKICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZUN1cnJlbnRDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMuJGVtaXQoJ3BhZ2luYXRpb24nLCB7CiAgICAgICAgcGFnZTogdmFsLAogICAgICAgIGxpbWl0OiB0aGlzLnBhZ2VTaXplCiAgICAgIH0pOwogICAgICBpZiAodGhpcy5hdXRvU2Nyb2xsKSB7CiAgICAgICAgKDAsIF9zY3JvbGxUby5zY3JvbGxUbykoMCwgODAwKTsKICAgICAgfQogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_scrollTo", "require", "name", "props", "total", "required", "type", "Number", "page", "default", "limit", "pageSizes", "Array", "layout", "String", "background", "Boolean", "autoScroll", "hidden", "computed", "currentPage", "get", "set", "val", "$emit", "pageSize", "methods", "handleSizeChange", "scrollTo", "handleCurrentChange"], "sources": ["src/components/Pagination/index.vue"], "sourcesContent": ["<template>\r\n  <div :class=\"{'hidden':hidden}\" class=\"pagination-container\">\r\n    <el-pagination\r\n      :background=\"background\"\r\n      :current-page.sync=\"currentPage\"\r\n      :page-size.sync=\"pageSize\"\r\n      :layout=\"layout\"\r\n      :page-sizes=\"pageSizes\"\r\n      :total=\"total\"\r\n      v-bind=\"$attrs\"\r\n      @size-change=\"handleSizeChange\"\r\n      @current-change=\"handleCurrentChange\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { scrollTo } from '@/utils/scroll-to'\r\n\r\nexport default {\r\n  name: 'Pagination',\r\n  props: {\r\n    total: {\r\n      required: true,\r\n      type: Number\r\n    },\r\n    page: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    limit: {\r\n      type: Number,\r\n      default: 20\r\n    },\r\n    pageSizes: {\r\n      type: Array,\r\n      default() {\r\n        return [10, 20, 30, 50]\r\n      }\r\n    },\r\n    layout: {\r\n      type: String,\r\n      default: 'total, sizes, prev, pager, next, jumper'\r\n    },\r\n    background: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    autoScroll: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    hidden: {\r\n      type: <PERSON>olean,\r\n      default: false\r\n    }\r\n  },\r\n  computed: {\r\n    currentPage: {\r\n      get() {\r\n        return this.page\r\n      },\r\n      set(val) {\r\n        this.$emit('update:page', val)\r\n      }\r\n    },\r\n    pageSize: {\r\n      get() {\r\n        return this.limit\r\n      },\r\n      set(val) {\r\n        this.$emit('update:limit', val)\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    handleSizeChange(val) {\r\n      this.$emit('pagination', { page: this.currentPage, limit: val })\r\n      if (this.autoScroll) {\r\n        scrollTo(0, 800)\r\n      }\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.$emit('pagination', { page: val, limit: this.pageSize })\r\n      if (this.autoScroll) {\r\n        scrollTo(0, 800)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.pagination-container {\r\n  background: #fff;\r\n  padding: 32px 16px;\r\n}\r\n.pagination-container.hidden {\r\n  display: none;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAiBA,IAAAA,SAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;kCAEA;EACAC,IAAA;EACAC,KAAA;IACAC,KAAA;MACAC,QAAA;MACAC,IAAA,EAAAC;IACA;IACAC,IAAA;MACAF,IAAA,EAAAC,MAAA;MACAE,OAAA;IACA;IACAC,KAAA;MACAJ,IAAA,EAAAC,MAAA;MACAE,OAAA;IACA;IACAE,SAAA;MACAL,IAAA,EAAAM,KAAA;MACAH,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAI,MAAA;MACAP,IAAA,EAAAQ,MAAA;MACAL,OAAA;IACA;IACAM,UAAA;MACAT,IAAA,EAAAU,OAAA;MACAP,OAAA;IACA;IACAQ,UAAA;MACAX,IAAA,EAAAU,OAAA;MACAP,OAAA;IACA;IACAS,MAAA;MACAZ,IAAA,EAAAU,OAAA;MACAP,OAAA;IACA;EACA;EACAU,QAAA;IACAC,WAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAb,IAAA;MACA;MACAc,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAC,KAAA,gBAAAD,GAAA;MACA;IACA;IACAE,QAAA;MACAJ,GAAA,WAAAA,IAAA;QACA,YAAAX,KAAA;MACA;MACAY,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAC,KAAA,iBAAAD,GAAA;MACA;IACA;EACA;EACAG,OAAA;IACAC,gBAAA,WAAAA,iBAAAJ,GAAA;MACA,KAAAC,KAAA;QAAAhB,IAAA,OAAAY,WAAA;QAAAV,KAAA,EAAAa;MAAA;MACA,SAAAN,UAAA;QACA,IAAAW,kBAAA;MACA;IACA;IACAC,mBAAA,WAAAA,oBAAAN,GAAA;MACA,KAAAC,KAAA;QAAAhB,IAAA,EAAAe,GAAA;QAAAb,KAAA,OAAAe;MAAA;MACA,SAAAR,UAAA;QACA,IAAAW,kBAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}