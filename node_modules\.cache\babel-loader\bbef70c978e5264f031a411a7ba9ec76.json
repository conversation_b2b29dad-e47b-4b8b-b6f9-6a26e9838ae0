{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\layout\\components\\Sidebar\\Logo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\layout\\components\\Sidebar\\Logo.vue", "mtime": 1718070340312}, {"path": "D:\\project_about\\git174\\alznt-admin\\babel.config.js", "mtime": 1718238659557}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9wcm9qZWN0X2Fib3V0L2dpdDE3NC9hbHpudC1hZG1pbi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9sb2dvID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL2Fzc2V0cy9sb2dvL2xvZ28ucG5nIikpOwp2YXIgX3ZhcmlhYmxlczIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvYXNzZXRzL3N0eWxlcy92YXJpYWJsZXMuc2NzcyIpKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICdTaWRlYmFyTG9nbycsCiAgcHJvcHM6IHsKICAgIGNvbGxhcHNlOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgdmFyaWFibGVzOiBmdW5jdGlvbiB2YXJpYWJsZXMoKSB7CiAgICAgIHJldHVybiBfdmFyaWFibGVzMi5kZWZhdWx0OwogICAgfSwKICAgIHNpZGVUaGVtZTogZnVuY3Rpb24gc2lkZVRoZW1lKCkgewogICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUuc2V0dGluZ3Muc2lkZVRoZW1lOwogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHRpdGxlOiAn5byA5Y+R5Yy65LiA572R57uf566hJywKICAgICAgbG9nbzogX2xvZ28uZGVmYXVsdAogICAgfTsKICB9Cn07"}, {"version": 3, "names": ["_logo", "_interopRequireDefault", "require", "_variables2", "name", "props", "collapse", "type", "Boolean", "required", "computed", "variables", "sideTheme", "$store", "state", "settings", "data", "title", "logo", "logoImg"], "sources": ["src/layout/components/Sidebar/Logo.vue"], "sourcesContent": ["<template>\r\n  <div class=\"sidebar-logo-container\" :class=\"{'collapse':collapse}\" :style=\"{ backgroundColor: sideTheme === 'theme-dark' ? variables.menuBg : variables.menuLightBg }\">\r\n    <transition name=\"sidebarLogoFade\">\r\n      <router-link v-if=\"collapse\" key=\"collapse\" class=\"sidebar-logo-link\" to=\"/\">\r\n        <img v-if=\"logo\" :src=\"logo\" class=\"sidebar-logo\">\r\n        <h1 v-else class=\"sidebar-title\" :style=\"{ color: sideTheme === 'theme-dark' ? variables.sidebarTitle : variables.sidebarLightTitle }\">{{ title }} </h1>\r\n      </router-link>\r\n      <router-link v-else key=\"expand\" class=\"sidebar-logo-link\" to=\"/\">\r\n        <img v-if=\"logo\" :src=\"logo\" class=\"sidebar-logo\">\r\n        <h1 class=\"sidebar-title\" :style=\"{ color: sideTheme === 'theme-dark' ? variables.sidebarTitle : variables.sidebarLightTitle }\">{{ title }} </h1>\r\n      </router-link>\r\n    </transition>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport logoImg from '@/assets/logo/logo.png'\r\nimport variables from '@/assets/styles/variables.scss'\r\n\r\nexport default {\r\n  name: 'SidebarLogo',\r\n  props: {\r\n    collapse: {\r\n      type: Boolean,\r\n      required: true\r\n    }\r\n  },\r\n  computed: {\r\n    variables() {\r\n      return variables;\r\n    },\r\n\tsideTheme() {\r\n      return this.$store.state.settings.sideTheme\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      title: '开发区一网统管',\r\n      logo: logoImg\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.sidebarLogoFade-enter-active {\r\n  transition: opacity 1.5s;\r\n}\r\n\r\n.sidebarLogoFade-enter,\r\n.sidebarLogoFade-leave-to {\r\n  opacity: 0;\r\n}\r\n\r\n.sidebar-logo-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 50px;\r\n  line-height: 50px;\r\n  background: #2b2f3a;\r\n  text-align: center;\r\n  overflow: hidden;\r\n\r\n  & .sidebar-logo-link {\r\n    height: 100%;\r\n    width: 100%;\r\n\r\n    & .sidebar-logo {\r\n      width: 32px;\r\n      height: 32px;\r\n      vertical-align: middle;\r\n      margin-right: 12px;\r\n    }\r\n\r\n    & .sidebar-title {\r\n      display: inline-block;\r\n      margin: 0;\r\n      color: #fff;\r\n      font-weight: 600;\r\n      line-height: 50px;\r\n      font-size: 14px;\r\n      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;\r\n      vertical-align: middle;\r\n    }\r\n  }\r\n\r\n  &.collapse {\r\n    .sidebar-logo {\r\n      margin-right: 0px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAgBA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAF,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,QAAA;IACA;EACA;EACAC,QAAA;IACAC,SAAA,WAAAA,UAAA;MACA,OAAAA,mBAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,SAAA;IACA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;MACAC,IAAA,EAAAC;IACA;EACA;AACA", "ignoreList": []}]}