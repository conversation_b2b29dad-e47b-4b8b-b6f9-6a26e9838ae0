{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\ThemePicker\\index.vue?vue&type=template&id=50c07b7a", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\ThemePicker\\index.vue", "mtime": 1718070340305}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1758071062035}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxlbC1jb2xvci1waWNrZXIKICB2LW1vZGVsPSJ0aGVtZSIKICA6cHJlZGVmaW5lPSJbJyM0MDlFRkYnLCAnIzE4OTBmZicsICcjMzA0MTU2JywnIzIxMjEyMScsJyMxMWE5ODMnLCAnIzEzYzJjMicsICcjNjk1OUNEJywgJyNmNTIyMmQnLCBdIgogIGNsYXNzPSJ0aGVtZS1waWNrZXIiCiAgcG9wcGVyLWNsYXNzPSJ0aGVtZS1waWNrZXItZHJvcGRvd24iCi8+Cg=="}, null]}