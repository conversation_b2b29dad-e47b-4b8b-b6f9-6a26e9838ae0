{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\map\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\map\\index.vue", "mtime": 1740448089611}, {"path": "D:\\project_about\\git174\\alznt-admin\\babel.config.js", "mtime": 1718238659557}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnaW5kZXgnLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4ge307CiAgfSwKICBjb21wdXRlZDoge30sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHRoaXMuaW5pdE1hcCgpOwogIH0sCiAgbWV0aG9kczogewogICAgaW5pdE1hcDogZnVuY3Rpb24gaW5pdE1hcCgpIHsKICAgICAgd2luZG93LkFyY0dpc1V0aWxzLmluaXRTY2VuZVZpZXcoewogICAgICAgIGRpdklkOiAnbWFwSW5zdGFuY2UnCiAgICAgIH0pOwogICAgICB0aGlzLmdldFBvaW50KCk7CiAgICB9LAogICAgZ2V0UG9pbnQ6IGZ1bmN0aW9uIGdldFBvaW50KCkgewogICAgICB2YXIgdGhhdCA9IHRoaXM7CiAgICAgIHZhciBjbG9jayA9IHNldEludGVydmFsKGZ1bmN0aW9uICgpIHsKICAgICAgICB2YXIgX3dpbmRvdzsKICAgICAgICBpZiAoKF93aW5kb3cgPSB3aW5kb3cpICE9PSBudWxsICYmIF93aW5kb3cgIT09IHZvaWQgMCAmJiBfd2luZG93LnZpZXcpIHsKICAgICAgICAgIGNsZWFySW50ZXJ2YWwoY2xvY2spOwogICAgICAgICAgd2luZG93LkFyY0dpc1V0aWxzLm1hcENsaWNrRXZlbnRIYW5kbGUuYWRkQ29vcmRpbmF0ZUxpc3RlbmVyKGZ1bmN0aW9uIChwb2ludCkgewogICAgICAgICAgICB0aGF0LiRlbWl0KCJtYXBDbGljayIsIHBvaW50KTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSwgMTAwMCk7CiAgICB9CiAgfSwKICB3YXRjaDoge30KfTs="}, {"version": 3, "names": ["name", "data", "computed", "mounted", "initMap", "methods", "window", "ArcGisUtils", "initSceneView", "divId", "getPoint", "that", "clock", "setInterval", "_window", "view", "clearInterval", "mapClickEventHandle", "addCoordinateListener", "point", "$emit", "watch"], "sources": ["src/components/map/index.vue"], "sourcesContent": ["<template>\r\n    <div class=\"mapContainer\" id=\"mapInstance\" />\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'index',\r\n  data() {\r\n    return {}\r\n  },\r\n  computed: {},\r\n  mounted() {\r\n    this.initMap()\r\n  },\r\n  methods: {\r\n    initMap () {\r\n      window.ArcGisUtils.initSceneView({ divId: 'mapInstance' })\r\n      this.getPoint()\r\n    },\r\n    getPoint() {\r\n      const that = this;\r\n      const clock = setInterval(function () {\r\n        if (window?.view) {\r\n          clearInterval(clock);\r\n          window.ArcGisUtils.mapClickEventHandle.addCoordinateListener((point) => {\r\n            that.$emit(\"mapClick\",point)\r\n          });\r\n        }\r\n      }, 1000);\r\n    }\r\n  },\r\n  watch: {}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n.mapContainer {\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 1;\r\n  background-color: #052c4d;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;iCAKA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;EACA;EACAC,QAAA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MACAE,MAAA,CAAAC,WAAA,CAAAC,aAAA;QAAAC,KAAA;MAAA;MACA,KAAAC,QAAA;IACA;IACAA,QAAA,WAAAA,SAAA;MACA,IAAAC,IAAA;MACA,IAAAC,KAAA,GAAAC,WAAA;QAAA,IAAAC,OAAA;QACA,KAAAA,OAAA,GAAAR,MAAA,cAAAQ,OAAA,eAAAA,OAAA,CAAAC,IAAA;UACAC,aAAA,CAAAJ,KAAA;UACAN,MAAA,CAAAC,WAAA,CAAAU,mBAAA,CAAAC,qBAAA,WAAAC,KAAA;YACAR,IAAA,CAAAS,KAAA,aAAAD,KAAA;UACA;QACA;MACA;IACA;EACA;EACAE,KAAA;AACA", "ignoreList": []}]}