{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\ThemePicker\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\ThemePicker\\index.vue", "mtime": 1718070340305}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;AAUA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/ThemePicker", "sourcesContent": ["<template>\r\n  <el-color-picker\r\n    v-model=\"theme\"\r\n    :predefine=\"['#409EFF', '#1890ff', '#304156','#212121','#11a983', '#13c2c2', '#6959CD', '#f5222d', ]\"\r\n    class=\"theme-picker\"\r\n    popper-class=\"theme-picker-dropdown\"\r\n  />\r\n</template>\r\n\r\n<script>\r\nconst version = require('element-ui/package.json').version // element-ui version from node_modules\r\nconst ORIGINAL_THEME = '#409EFF' // default color\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      chalk: '', // content of theme-chalk css\r\n      theme: ''\r\n    }\r\n  },\r\n  computed: {\r\n    defaultTheme() {\r\n      return this.$store.state.settings.theme\r\n    }\r\n  },\r\n  watch: {\r\n    defaultTheme: {\r\n      handler: function(val, oldVal) {\r\n        this.theme = val\r\n      },\r\n      immediate: true\r\n    },\r\n    async theme(val) {\r\n      const oldVal = this.chalk ? this.theme : ORIGINAL_THEME\r\n      if (typeof val !== 'string') return\r\n      const themeCluster = this.getThemeCluster(val.replace('#', ''))\r\n      const originalCluster = this.getThemeCluster(oldVal.replace('#', ''))\r\n      console.log(themeCluster, originalCluster)\r\n\r\n      const $message = this.$message({\r\n        message: '  Compiling the theme',\r\n        customClass: 'theme-message',\r\n        type: 'success',\r\n        duration: 0,\r\n        iconClass: 'el-icon-loading'\r\n      })\r\n\r\n      const getHandler = (variable, id) => {\r\n        return () => {\r\n          const originalCluster = this.getThemeCluster(ORIGINAL_THEME.replace('#', ''))\r\n          const newStyle = this.updateStyle(this[variable], originalCluster, themeCluster)\r\n\r\n          let styleTag = document.getElementById(id)\r\n          if (!styleTag) {\r\n            styleTag = document.createElement('style')\r\n            styleTag.setAttribute('id', id)\r\n            document.head.appendChild(styleTag)\r\n          }\r\n          styleTag.innerText = newStyle\r\n        }\r\n      }\r\n\r\n      if (!this.chalk) {\r\n        const url = `https://unpkg.com/element-ui@${version}/lib/theme-chalk/index.css`\r\n        await this.getCSSString(url, 'chalk')\r\n      }\r\n\r\n      const chalkHandler = getHandler('chalk', 'chalk-style')\r\n\r\n      chalkHandler()\r\n\r\n      const styles = [].slice.call(document.querySelectorAll('style'))\r\n        .filter(style => {\r\n          const text = style.innerText\r\n          return new RegExp(oldVal, 'i').test(text) && !/Chalk Variables/.test(text)\r\n        })\r\n      styles.forEach(style => {\r\n        const { innerText } = style\r\n        if (typeof innerText !== 'string') return\r\n        style.innerText = this.updateStyle(innerText, originalCluster, themeCluster)\r\n      })\r\n\r\n      this.$emit('change', val)\r\n\r\n      $message.close()\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    updateStyle(style, oldCluster, newCluster) {\r\n      let newStyle = style\r\n      oldCluster.forEach((color, index) => {\r\n        newStyle = newStyle.replace(new RegExp(color, 'ig'), newCluster[index])\r\n      })\r\n      return newStyle\r\n    },\r\n\r\n    getCSSString(url, variable) {\r\n      return new Promise(resolve => {\r\n        const xhr = new XMLHttpRequest()\r\n        xhr.onreadystatechange = () => {\r\n          if (xhr.readyState === 4 && xhr.status === 200) {\r\n            this[variable] = xhr.responseText.replace(/@font-face{[^}]+}/, '')\r\n            resolve()\r\n          }\r\n        }\r\n        xhr.open('GET', url)\r\n        xhr.send()\r\n      })\r\n    },\r\n\r\n    getThemeCluster(theme) {\r\n      const tintColor = (color, tint) => {\r\n        let red = parseInt(color.slice(0, 2), 16)\r\n        let green = parseInt(color.slice(2, 4), 16)\r\n        let blue = parseInt(color.slice(4, 6), 16)\r\n\r\n        if (tint === 0) { // when primary color is in its rgb space\r\n          return [red, green, blue].join(',')\r\n        } else {\r\n          red += Math.round(tint * (255 - red))\r\n          green += Math.round(tint * (255 - green))\r\n          blue += Math.round(tint * (255 - blue))\r\n\r\n          red = red.toString(16)\r\n          green = green.toString(16)\r\n          blue = blue.toString(16)\r\n\r\n          return `#${red}${green}${blue}`\r\n        }\r\n      }\r\n\r\n      const shadeColor = (color, shade) => {\r\n        let red = parseInt(color.slice(0, 2), 16)\r\n        let green = parseInt(color.slice(2, 4), 16)\r\n        let blue = parseInt(color.slice(4, 6), 16)\r\n\r\n        red = Math.round((1 - shade) * red)\r\n        green = Math.round((1 - shade) * green)\r\n        blue = Math.round((1 - shade) * blue)\r\n\r\n        red = red.toString(16)\r\n        green = green.toString(16)\r\n        blue = blue.toString(16)\r\n\r\n        return `#${red}${green}${blue}`\r\n      }\r\n\r\n      const clusters = [theme]\r\n      for (let i = 0; i <= 9; i++) {\r\n        clusters.push(tintColor(theme, Number((i / 10).toFixed(2))))\r\n      }\r\n      clusters.push(shadeColor(theme, 0.1))\r\n      return clusters\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.theme-message,\r\n.theme-picker-dropdown {\r\n  z-index: 99999 !important;\r\n}\r\n\r\n.theme-picker .el-color-picker__trigger {\r\n  height: 26px !important;\r\n  width: 26px !important;\r\n  padding: 2px;\r\n}\r\n\r\n.theme-picker-dropdown .el-color-dropdown__link-btn {\r\n  display: none;\r\n}\r\n</style>\r\n"]}]}