{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\map\\index.vue?vue&type=style&index=0&id=2bc4c202&scoped=true&lang=less", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\map\\index.vue", "mtime": 1740448089611}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758071060223}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758071061987}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758071060880}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1758071059610}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5tYXBDb250YWluZXIgew0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiAxMDAlOw0KICB6LWluZGV4OiAxOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjMDUyYzRkOw0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/map", "sourcesContent": ["<template>\r\n    <div class=\"mapContainer\" id=\"mapInstance\" />\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'index',\r\n  data() {\r\n    return {}\r\n  },\r\n  computed: {},\r\n  mounted() {\r\n    this.initMap()\r\n  },\r\n  methods: {\r\n    initMap () {\r\n      window.ArcGisUtils.initSceneView({ divId: 'mapInstance' })\r\n      this.getPoint()\r\n    },\r\n    getPoint() {\r\n      const that = this;\r\n      const clock = setInterval(function () {\r\n        if (window?.view) {\r\n          clearInterval(clock);\r\n          window.ArcGisUtils.mapClickEventHandle.addCoordinateListener((point) => {\r\n            that.$emit(\"mapClick\",point)\r\n          });\r\n        }\r\n      }, 1000);\r\n    }\r\n  },\r\n  watch: {}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n.mapContainer {\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 1;\r\n  background-color: #052c4d;\r\n}\r\n</style>\r\n"]}]}