{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\layout\\components\\Sidebar\\SidebarItem.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\layout\\components\\Sidebar\\SidebarItem.vue", "mtime": 1718070340312}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["SidebarItem.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SidebarItem.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<template>\r\n  <div v-if=\"!item.hidden\">\r\n    <template v-if=\"hasOneShowingChild(item.children,item) && (!onlyOneChild.children||onlyOneChild.noShowingChildren)&&!item.alwaysShow\">\r\n      <app-link v-if=\"onlyOneChild.meta\" :to=\"resolvePath(onlyOneChild.path)\">\r\n        <el-menu-item :index=\"resolvePath(onlyOneChild.path)\" :class=\"{'submenu-title-noDropdown':!isNest}\">\r\n          <item :icon=\"onlyOneChild.meta.icon||(item.meta&&item.meta.icon)\" :title=\"onlyOneChild.meta.title\" />\r\n        </el-menu-item>\r\n      </app-link>\r\n    </template>\r\n\r\n    <el-submenu v-else ref=\"subMenu\" :index=\"resolvePath(item.path)\" popper-append-to-body>\r\n      <template slot=\"title\">\r\n        <item v-if=\"item.meta\" :icon=\"item.meta && item.meta.icon\" :title=\"item.meta.title\" />\r\n      </template>\r\n      <sidebar-item\r\n        v-for=\"child in item.children\"\r\n        :key=\"child.path\"\r\n        :is-nest=\"true\"\r\n        :item=\"child\"\r\n        :base-path=\"resolvePath(child.path)\"\r\n        class=\"nest-menu\"\r\n      />\r\n    </el-submenu>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport path from 'path'\r\nimport { isExternal } from '@/utils/validate'\r\nimport Item from './Item'\r\nimport AppLink from './Link'\r\nimport FixiOSBug from './FixiOSBug'\r\n\r\nexport default {\r\n  name: 'SidebarItem',\r\n  components: { Item, AppLink },\r\n  mixins: [FixiOSBug],\r\n  props: {\r\n    // route object\r\n    item: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    isNest: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    basePath: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    this.onlyOneChild = null\r\n    return {}\r\n  },\r\n  methods: {\r\n    hasOneShowingChild(children = [], parent) {\r\n      if (!children) {\r\n        children = [];\r\n      }\r\n      const showingChildren = children.filter(item => {\r\n        if (item.hidden) {\r\n          return false\r\n        } else {\r\n          // Temp set(will be used if only has one showing child)\r\n          this.onlyOneChild = item\r\n          return true\r\n        }\r\n      })\r\n\r\n      // When there is only one child router, the child router is displayed by default\r\n      if (showingChildren.length === 1) {\r\n        return true\r\n      }\r\n\r\n      // Show parent if there are no child router to display\r\n      if (showingChildren.length === 0) {\r\n        this.onlyOneChild = { ... parent, path: '', noShowingChildren: true }\r\n        return true\r\n      }\r\n\r\n      return false\r\n    },\r\n    resolvePath(routePath) {\r\n      if (isExternal(routePath)) {\r\n        return routePath\r\n      }\r\n      if (isExternal(this.basePath)) {\r\n        return this.basePath\r\n      }\r\n      return path.resolve(this.basePath, routePath)\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}