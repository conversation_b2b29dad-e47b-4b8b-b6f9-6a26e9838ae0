{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\easyCron\\tabs\\year.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\easyCron\\tabs\\year.vue", "mtime": 1718070340308}, {"path": "D:\\project_about\\git174\\alznt-admin\\babel.config.js", "mtime": 1718238659557}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9wcm9qZWN0X2Fib3V0L2dpdDE3NC9hbHpudC1hZG1pbi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9taXhpbiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9taXhpbiIpKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICd5ZWFyJywKICBtaXhpbnM6IFtfbWl4aW4uZGVmYXVsdF0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7fTsKICB9LAogIHdhdGNoOiB7CiAgICB2YWx1ZV9jOiBmdW5jdGlvbiB2YWx1ZV9jKG5ld1ZhbCwgb2xkVmFsKSB7CiAgICAgIC8vIGNvbnNvbGUuaW5mbygnY2hhbmdlOicgKyBuZXdWYWwpCiAgICAgIHRoaXMuJGVtaXQoJ2NoYW5nZScsIG5ld1ZhbCk7CiAgICB9CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdmFyIG5vd1llYXIgPSBuZXcgRGF0ZSgpLmdldEZ1bGxZZWFyKCk7CiAgICB0aGlzLkRFRkFVTFRfVkFMVUUgPSAnKic7CiAgICB0aGlzLm1pblZhbHVlID0gMDsKICAgIHRoaXMubWF4VmFsdWUgPSAwOwogICAgdGhpcy52YWx1ZVJhbmdlLnN0YXJ0ID0gbm93WWVhcjsKICAgIHRoaXMudmFsdWVSYW5nZS5lbmQgPSBub3dZZWFyICsgMTAwOwogICAgdGhpcy52YWx1ZUxvb3Auc3RhcnQgPSBub3dZZWFyOwogICAgdGhpcy52YWx1ZUxvb3AuaW50ZXJ2YWwgPSAxOwogICAgLy8gY29uc29sZS5pbmZvKCdjcmVhdGVkJykKICAgIHRoaXMucGFyc2VQcm9wKHRoaXMucHJvcCk7CiAgfQp9Ow=="}, {"version": 3, "names": ["_mixin", "_interopRequireDefault", "require", "name", "mixins", "mixin", "data", "watch", "value_c", "newVal", "oldVal", "$emit", "created", "nowYear", "Date", "getFullYear", "DEFAULT_VALUE", "minValue", "maxValue", "valueRange", "start", "end", "valueLoop", "interval", "parseProp", "prop"], "sources": ["src/components/easyCron/tabs/year.vue"], "sourcesContent": ["<template>\r\n  <div class=\"config-list\">\r\n    <RadioGroup v-model=\"type\">\r\n    <div class=\"item\">\r\n      <Radio label=\"TYPE_EVERY\" class=\"choice\" :disabled=\"disabled\">每年</Radio>\r\n    </div>\r\n    <div class=\"item\">\r\n      <Radio label=\"TYPE_RANGE\" class=\"choice\" :disabled=\"disabled\">区间</Radio>\r\n       从<InputNumber :disabled=\"type!=TYPE_RANGE || disabled\" :min=\"0\" :precision=\"0\"\r\n        class=\"w60\" v-model=\"valueRange.start\" />年\r\n       至<InputNumber :disabled=\"type!=TYPE_RANGE || disabled\" :min=\"1\" :precision=\"0\"\r\n        class=\"w60\" v-model=\"valueRange.end\" />年\r\n    </div>\r\n    <div class=\"item\">\r\n      <Radio label=\"TYPE_LOOP\" class=\"choice\" :disabled=\"disabled\">循环</Radio>\r\n      从<InputNumber :disabled=\"type!=TYPE_LOOP || disabled\" :min=\"0\" :precision=\"0\"\r\n       class=\"w60\" v-model=\"valueLoop.start\" />年开始，间隔\r\n      <InputNumber :disabled=\"type!=TYPE_LOOP || disabled\" :min=\"1\" :precision=\"0\"\r\n       class=\"w60\" v-model=\"valueLoop.interval\" />年\r\n    </div>\r\n    </RadioGroup>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport mixin from './mixin'\r\n\r\nexport default {\r\n  name: 'year',\r\n  mixins: [mixin],\r\n  data () {\r\n    return {}\r\n  },\r\n  watch: {\r\n    value_c (newVal, oldVal) {\r\n      // console.info('change:' + newVal)\r\n      this.$emit('change', newVal)\r\n    }\r\n  },\r\n  created () {\r\n    const nowYear = (new Date()).getFullYear()\r\n    this.DEFAULT_VALUE = '*'\r\n    this.minValue = 0\r\n    this.maxValue = 0\r\n    this.valueRange.start = nowYear\r\n    this.valueRange.end = nowYear + 100\r\n    this.valueLoop.start = nowYear\r\n    this.valueLoop.interval = 1\r\n    // console.info('created')\r\n    this.parseProp(this.prop)\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n.config-list {\r\n  text-align: left;\r\n  margin: 0 10px 10px 10px;\r\n}\r\n\r\n.item {\r\n  margin-top: 5px;\r\n}\r\n\r\n.choice {\r\n  border: 1px solid transparent;\r\n  padding: 5px 8px;\r\n}\r\n\r\n.choice:hover {\r\n  border: 1px solid #1890ff;\r\n}\r\n\r\n.w60 {\r\n  width: 60px;\r\n}\r\n\r\n.ivu-input-number {\r\n  margin-left: 5px;\r\n  margin-right: 5px;\r\n}\r\n\r\n.list {\r\n  margin: 0 20px;\r\n}\r\n\r\n.list-check-item {\r\n  padding: 1px 3px;\r\n  width: 4em;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAyBA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,MAAA,GAAAC,cAAA;EACAC,IAAA,WAAAA,KAAA;IACA;EACA;EACAC,KAAA;IACAC,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;MACA;MACA,KAAAC,KAAA,WAAAF,MAAA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,IAAAC,OAAA,OAAAC,IAAA,GAAAC,WAAA;IACA,KAAAC,aAAA;IACA,KAAAC,QAAA;IACA,KAAAC,QAAA;IACA,KAAAC,UAAA,CAAAC,KAAA,GAAAP,OAAA;IACA,KAAAM,UAAA,CAAAE,GAAA,GAAAR,OAAA;IACA,KAAAS,SAAA,CAAAF,KAAA,GAAAP,OAAA;IACA,KAAAS,SAAA,CAAAC,QAAA;IACA;IACA,KAAAC,SAAA,MAAAC,IAAA;EACA;AACA", "ignoreList": []}]}