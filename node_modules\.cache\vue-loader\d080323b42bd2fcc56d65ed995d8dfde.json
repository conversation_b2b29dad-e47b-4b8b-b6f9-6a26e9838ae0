{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\easyCron\\index.vue?vue&type=style&index=0&id=356b6c43&scoped=true&lang=css", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\easyCron\\index.vue", "mtime": 1718070340305}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758071060223}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758071061987}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758071060880}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmVhc3ktY3JvbiB7DQogIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgYm9yZGVyOiAxcHggc29saWQgIzE4OTBmZjsNCn0NCg0KLmNvbnRlbnQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LXdyYXA6IG5vd3JhcDsNCn0NCg0KLmxlZnQgew0KICBmbGV4LWJhc2lzOiA2MCU7DQogIHdpZHRoOiA2MCU7DQogIGJvcmRlcjogMXB4IHNvbGlkIHRyYW5zcGFyZW50Ow0KICBib3JkZXItcmlnaHQtY29sb3I6ICMxODkwZmY7DQp9DQoNCi5yaWdodCB7DQogIGZsZXgtYmFzaXM6IDQwJTsNCiAgd2lkdGg6IDQwJTsNCiAgLypib3JkZXI6IDFweCBzb2xpZCBkaW1ncmF5OyovDQp9DQoNCi5pdnUtdGFibGUtc21hbGwgdGQgew0KICBoZWlnaHQ6IDMwcHggIWltcG9ydGFudDsNCn0NCg0KLmV4ZS1wcmUgew0KICBtYXJnaW4tdG9wOiA1cHg7DQp9DQoNCi5leGUtcHJlID4gZGl2IHsNCiAgbWFyZ2luLXRvcDogNXB4Ow0KfQ0KDQouZXhlLXByZS1wYW5lbCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDsNCn0NCg0KLmV4ZS1wcmUtcGFuZWwgLnAtbGVmdCB7DQogIGZsZXgtYmFzaXM6IDgwcHg7DQogIGZsZXgtZ3JvdzogMDsNCn0NCg0KLmV4ZS1wcmUtcGFuZWwgLnAtcmlnaHQgew0KICBmbGV4LWJhc2lzOiAxMDBweDsNCiAgZmxleC1ncm93OiAxOw0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmLA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/easyCron", "sourcesContent": ["<template>\r\n  <Card class=\"easy-cron\">\r\n    <div class=\"content\">\r\n      <div class=\"left\">\r\n        <Tabs size=\"small\" v-model=\"curtab\">\r\n          <TabPane label=\"秒\" name=\"second\"><second-ui v-model=\"second\" :disabled=\"disabled\"></second-ui></TabPane>\r\n          <TabPane label=\"分\" name=\"minute\"><minute-ui v-model=\"minute\" :disabled=\"disabled\"></minute-ui></TabPane>\r\n          <TabPane label=\"时\" name=\"hour\"><hour-ui v-model=\"hour\" :disabled=\"disabled\"></hour-ui></TabPane>\r\n          <TabPane label=\"日\" name=\"day\"><day-ui v-model=\"day\" :week=\"week\" :disabled=\"disabled\"></day-ui></TabPane>\r\n          <TabPane label=\"月\" name=\"month\"><month-ui v-model=\"month\" :disabled=\"disabled\"></month-ui></TabPane>\r\n          <TabPane label=\"周\" name=\"week\"><week-ui v-model=\"week\" :day=\"day\" :disabled=\"disabled\"></week-ui></TabPane>\r\n          <TabPane label=\"年\" name=\"year\"><year-ui v-model=\"year\" :disabled=\"disabled\"></year-ui></TabPane>\r\n        </Tabs>\r\n      </div>\r\n      <div class=\"right\">\r\n        <div class=\"field-list\"><Table stripe :columns=\"columns\" :data=\"tableData\"\r\n          :show-header=\"false\" size=\"small\"></Table></div>\r\n        <div class=\"exe-pre\">\r\n          <div class=\"exe-pre-panel\">\r\n            <label class=\"p-left\">执行时间</label>\r\n            <DatePicker type=\"datetime\" v-model=\"startTime\" class=\"p-right\" @on-change=\"calTriggerList\"\r\n              placeholder=\"选择执行开始时间\"></DatePicker>\r\n          </div>\r\n          <div class=\"exe-pre-panel\">\r\n            <Tooltip content=\"执行预览解析不含年参数\" class=\"p-left\">\r\n              <label>执行预览</label>\r\n            </Tooltip>\r\n            <Input type=\"textarea\" :value=\"preTimeList\" class=\"p-right\" :rows=\"4\" readonly />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </Card>\r\n</template>\r\n\r\n<script>\r\nimport SecondUi from './tabs/second'\r\nimport MinuteUi from './tabs/minute'\r\nimport HourUi from './tabs/hour'\r\nimport DayUi from './tabs/day'\r\nimport WeekUi from './tabs/week'\r\nimport MonthUi from './tabs/month'\r\nimport YearUi from './tabs/year'\r\nimport CronParser from 'cron-parser'\r\nimport dateFormat from './format-date'\r\n\r\nexport default {\r\n  name: 'easy-cron',\r\n  model: {\r\n    prop: 'cronValue',\r\n    event: 'change'\r\n  },\r\n  props: {\r\n    cronValue: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data () {\r\n    return {\r\n      curtab: '',\r\n      second: '*',\r\n      minute: '*',\r\n      hour: '*',\r\n      day: '*',\r\n      month: '*',\r\n      week: '?',\r\n      year: '*',\r\n      startTime: new Date(),\r\n      preTimeList: '执行预览，会忽略年份参数',\r\n      columns: [\r\n        // {title: ' ', width: '80', key: 'name'}, {title: ' ', key: 'value', align: 'left'}\r\n        {title: ' ', width: '80', key: 'name'}\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    tableData () {\r\n      // return [\r\n      //   { name: '秒', value: this.second },\r\n      //   { name: '分', value: this.minute },\r\n      //   { name: '时', value: this.hour },\r\n      //   { name: '日', value: this.day },\r\n      //   { name: '月', value: this.month },\r\n      //   { name: '周', value: this.week },\r\n      //   { name: '年', value: this.year },\r\n      //   { name: '表达式', value: this.cronValue_c },\r\n      //   { name: '表达式(不含年)', value: this.cronValue_c2 }\r\n      // ]\r\n      return [\r\n        {name: '秒：' + this.second},\r\n        {name: '分：' + this.minute},\r\n        {name: '时：' + this.hour},\r\n        {name: '日：' + this.day},\r\n        {name: '月：' + this.month},\r\n        {name: '周：' + this.week},\r\n        {name: '年：' + this.year},\r\n        {name: '表达式：' + this.cronValue_c},\r\n        {name: '表达式(不含年)：' + this.cronValue_c2}\r\n      ]\r\n    },\r\n    cronValue_c () {\r\n      let result = []\r\n      result.push(this.second ? this.second : '*')\r\n      result.push(this.minute ? this.minute : '*')\r\n      result.push(this.hour ? this.hour : '*')\r\n      result.push(this.day ? this.day : '*')\r\n      result.push(this.month ? this.month : '*')\r\n      result.push(this.week ? this.week : '?')\r\n      result.push(this.year ? this.year : '*')\r\n      return result.join(' ')\r\n    },\r\n    cronValue_c2 () {\r\n      return this.cronValue_c.split(' ').slice(0, 6).join(' ')\r\n    }\r\n  },\r\n  watch: {\r\n    cronValue (newVal, oldVal) {\r\n      if (newVal === this.cronValue_c) {\r\n        // console.info('same cron value: ' + newVal)\r\n        return\r\n      }\r\n      this.formatValue()\r\n    },\r\n    cronValue_c (newVal, oldVal) {\r\n      this.calTriggerList()\r\n      this.$emit('change', newVal)\r\n    }\r\n  },\r\n  methods: {\r\n    formatValue () {\r\n      // console.info(this.cronValue)\r\n      if (!this.cronValue) return\r\n      const values = this.cronValue.split(' ').filter(item => !!item)\r\n      if (!values || values.length <= 0) return\r\n      this.second = values[0]\r\n      if (values.length > 1) this.minute = values[1]\r\n      if (values.length > 2) this.hour = values[2]\r\n      if (values.length > 3) this.day = values[3]\r\n      if (values.length > 4) this.month = values[4]\r\n      if (values.length > 5) this.week = values[5]\r\n      if (values.length > 6) this.year = values[6]\r\n    },\r\n    calTriggerList () {\r\n      // 去掉年份参数\r\n      const e = this.cronValue_c2\r\n      const format = 'yyyy-MM-dd hh:mm:ss'\r\n      const options = {\r\n        currentDate: dateFormat(this.startTime, format)\r\n      }\r\n      // console.info(options)\r\n      const iter = CronParser.parseExpression(e, options)\r\n      const result = []\r\n      for (let i = 0; i < 5; i++) {\r\n        result.push(dateFormat(new Date(iter.next()), format))\r\n      }\r\n      this.preTimeList = result.length > 0 ? result.join('\\n') : '无执行时间'\r\n    }\r\n  },\r\n  components: {\r\n    SecondUi,\r\n    MinuteUi,\r\n    HourUi,\r\n    DayUi,\r\n    WeekUi,\r\n    MonthUi,\r\n    YearUi\r\n  },\r\n  created () {\r\n    this.formatValue()\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.easy-cron {\r\n  display: inline-block;\r\n  border: 1px solid #1890ff;\r\n}\r\n\r\n.content {\r\n  display: flex;\r\n  flex-wrap: nowrap;\r\n}\r\n\r\n.left {\r\n  flex-basis: 60%;\r\n  width: 60%;\r\n  border: 1px solid transparent;\r\n  border-right-color: #1890ff;\r\n}\r\n\r\n.right {\r\n  flex-basis: 40%;\r\n  width: 40%;\r\n  /*border: 1px solid dimgray;*/\r\n}\r\n\r\n.ivu-table-small td {\r\n  height: 30px !important;\r\n}\r\n\r\n.exe-pre {\r\n  margin-top: 5px;\r\n}\r\n\r\n.exe-pre > div {\r\n  margin-top: 5px;\r\n}\r\n\r\n.exe-pre-panel {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n}\r\n\r\n.exe-pre-panel .p-left {\r\n  flex-basis: 80px;\r\n  flex-grow: 0;\r\n}\r\n\r\n.exe-pre-panel .p-right {\r\n  flex-basis: 100px;\r\n  flex-grow: 1;\r\n}\r\n</style>\r\n"]}]}