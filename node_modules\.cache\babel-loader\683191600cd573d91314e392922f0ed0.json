{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\RuoYi\\Doc\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\RuoYi\\Doc\\index.vue", "mtime": 1718070340302}, {"path": "D:\\project_about\\git174\\alznt-admin\\babel.config.js", "mtime": 1718238659557}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnUnVvWWlEb2MnLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB1cmw6ICdodHRwOi8vZG9jLnJ1b3lpLnZpcC9ydW95aS12dWUnCiAgICB9OwogIH0sCiAgbWV0aG9kczogewogICAgZ290bzogZnVuY3Rpb24gZ290bygpIHsKICAgICAgd2luZG93Lm9wZW4odGhpcy51cmwpOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["name", "data", "url", "methods", "goto", "window", "open"], "sources": ["src/components/RuoYi/Doc/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <svg-icon icon-class=\"question\" @click=\"goto\"/>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'RuoYiDoc',\r\n  data() {\r\n    return {\r\n      url: 'http://doc.ruoyi.vip/ruoyi-vue'\r\n    }\r\n  },\r\n  methods: {\r\n    goto() {\r\n      window.open(this.url)\r\n    }\r\n  }\r\n}\r\n</script>"], "mappings": ";;;;;;;;;;;;iCAOA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,GAAA;IACA;EACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAA;MACAC,MAAA,CAAAC,IAAA,MAAAJ,GAAA;IACA;EACA;AACA", "ignoreList": []}]}