{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\Breadcrumb\\index.vue?vue&type=template&id=b50ef614&scoped=true", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\Breadcrumb\\index.vue", "mtime": 1718070340298}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1758071062035}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxlbC1icmVhZGNydW1iIGNsYXNzPSJhcHAtYnJlYWRjcnVtYiIgc2VwYXJhdG9yPSIvIj4KICA8dHJhbnNpdGlvbi1ncm91cCBuYW1lPSJicmVhZGNydW1iIj4KICAgIDxlbC1icmVhZGNydW1iLWl0ZW0gdi1mb3I9IihpdGVtLGluZGV4KSBpbiBsZXZlbExpc3QiIDprZXk9Iml0ZW0ucGF0aCI+CiAgICAgIDxzcGFuIHYtaWY9Iml0ZW0ucmVkaXJlY3Q9PT0nbm9SZWRpcmVjdCd8fGluZGV4PT1sZXZlbExpc3QubGVuZ3RoLTEiIGNsYXNzPSJuby1yZWRpcmVjdCI+e3sgaXRlbS5tZXRhLnRpdGxlIH19PC9zcGFuPgogICAgICA8YSB2LWVsc2UgQGNsaWNrLnByZXZlbnQ9ImhhbmRsZUxpbmsoaXRlbSkiPnt7IGl0ZW0ubWV0YS50aXRsZSB9fTwvYT4KICAgIDwvZWwtYnJlYWRjcnVtYi1pdGVtPgogIDwvdHJhbnNpdGlvbi1ncm91cD4KPC9lbC1icmVhZGNydW1iPgo="}, null]}