{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\ImageUpload\\index.vue?vue&type=style&index=0&id=82a94682&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\ImageUpload\\index.vue", "mtime": 1718070340300}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758071060223}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758071061987}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758071060880}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758071059610}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouaW1hZ2Ugew0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIC5tYXNrIHsNCiAgICBvcGFjaXR5OiAwOw0KICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICB0b3A6IDA7DQogICAgd2lkdGg6IDEwMCU7DQogICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAwLCAwLCAwLjUpOw0KICAgIHRyYW5zaXRpb246IGFsbCAwLjNzOw0KICB9DQogICY6aG92ZXIgLm1hc2sgew0KICAgIG9wYWNpdHk6IDE7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/ImageUpload", "sourcesContent": ["<template>\r\n  <div class=\"component-upload-image\">\r\n    <el-upload\r\n      :action=\"uploadImgUrl\"\r\n      list-type=\"picture-card\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n      :on-error=\"handleUploadError\"\r\n      name=\"file\"\r\n      :show-file-list=\"false\"\r\n      :headers=\"headers\"\r\n      style=\"display: inline-block; vertical-align: top\"\r\n    >\r\n      <el-image v-if=\"!value\" :src=\"value\">\r\n        <div slot=\"error\" class=\"image-slot\">\r\n          <i class=\"el-icon-plus\" />\r\n        </div>\r\n      </el-image>\r\n      <div v-else class=\"image\">\r\n        <el-image :src=\"value\" :style=\"`width:150px;height:150px;`\" fit=\"fill\"/>\r\n        <div class=\"mask\">\r\n          <div class=\"actions\">\r\n            <span title=\"预览\" @click.stop=\"dialogVisible = true\">\r\n              <i class=\"el-icon-zoom-in\" />\r\n            </span>\r\n            <span title=\"移除\" @click.stop=\"removeImage\">\r\n              <i class=\"el-icon-delete\" />\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-upload>\r\n    <el-dialog :visible.sync=\"dialogVisible\" title=\"预览\" width=\"800\" append-to-body>\r\n      <img :src=\"value\" style=\"display: block; max-width: 100%; margin: 0 auto;\">\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      uploadImgUrl: process.env.VUE_APP_BASE_API + \"/common/upload\", // 上传的图片服务器地址\r\n      headers: {\r\n        Authorization: \"Bearer \" + getToken(),\r\n      },\r\n    };\r\n  },\r\n  props: {\r\n    value: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n  },\r\n  methods: {\r\n    removeImage() {\r\n      this.$emit(\"input\", \"\");\r\n    },\r\n    handleUploadSuccess(res) {\r\n      this.$emit(\"input\", res.url);\r\n      this.loading.close();\r\n    },\r\n    handleBeforeUpload() {\r\n      this.loading = this.$loading({\r\n        lock: true,\r\n        text: \"上传中\",\r\n        background: \"rgba(0, 0, 0, 0.7)\",\r\n      });\r\n    },\r\n    handleUploadError() {\r\n      this.$message({\r\n        type: \"error\",\r\n        message: \"上传失败\",\r\n      });\r\n      this.loading.close();\r\n    },\r\n  },\r\n  watch: {},\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.image {\r\n  position: relative;\r\n  .mask {\r\n    opacity: 0;\r\n    position: absolute;\r\n    top: 0;\r\n    width: 100%;\r\n    background-color: rgba(0, 0, 0, 0.5);\r\n    transition: all 0.3s;\r\n  }\r\n  &:hover .mask {\r\n    opacity: 1;\r\n  }\r\n}\r\n</style>"]}]}