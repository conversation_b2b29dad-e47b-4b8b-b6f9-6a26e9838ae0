{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\views\\dashboard\\BarChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\views\\dashboard\\BarChart.vue", "mtime": 1718070340323}, {"path": "D:\\project_about\\git174\\alznt-admin\\babel.config.js", "mtime": 1718238659557}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_echarts", "_interopRequireDefault", "require", "_resize", "animationDuration", "_default", "exports", "default", "mixins", "resize", "props", "className", "type", "String", "width", "height", "data", "chart", "mounted", "_this", "$nextTick", "initChart", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "methods", "echarts", "init", "$el", "setOption", "tooltip", "trigger", "axisPointer", "grid", "top", "left", "right", "bottom", "containLabel", "xAxis", "axisTick", "alignWithLabel", "yAxis", "show", "series", "name", "stack", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/dashboard/BarChart.vue"], "sourcesContent": ["<template>\r\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\r\n</template>\r\n\r\n<script>\r\nimport echarts from 'echarts'\r\nrequire('echarts/theme/macarons') // echarts theme\r\nimport resize from './mixins/resize'\r\n\r\nconst animationDuration = 6000\r\n\r\nexport default {\r\n  mixins: [resize],\r\n  props: {\r\n    className: {\r\n      type: String,\r\n      default: 'chart'\r\n    },\r\n    width: {\r\n      type: String,\r\n      default: '100%'\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '300px'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chart: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initChart()\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    if (!this.chart) {\r\n      return\r\n    }\r\n    this.chart.dispose()\r\n    this.chart = null\r\n  },\r\n  methods: {\r\n    initChart() {\r\n      this.chart = echarts.init(this.$el, 'macarons')\r\n\r\n      this.chart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { // 坐标轴指示器，坐标轴触发有效\r\n            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'\r\n          }\r\n        },\r\n        grid: {\r\n          top: 10,\r\n          left: '2%',\r\n          right: '2%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: [{\r\n          type: 'category',\r\n          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\r\n          axisTick: {\r\n            alignWithLabel: true\r\n          }\r\n        }],\r\n        yAxis: [{\r\n          type: 'value',\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        }],\r\n        series: [{\r\n          name: 'pageA',\r\n          type: 'bar',\r\n          stack: 'vistors',\r\n          barWidth: '60%',\r\n          data: [79, 52, 200, 334, 390, 330, 220],\r\n          animationDuration\r\n        }, {\r\n          name: 'pageB',\r\n          type: 'bar',\r\n          stack: 'vistors',\r\n          barWidth: '60%',\r\n          data: [80, 52, 200, 334, 390, 330, 220],\r\n          animationDuration\r\n        }, {\r\n          name: 'pageC',\r\n          type: 'bar',\r\n          stack: 'vistors',\r\n          barWidth: '60%',\r\n          data: [30, 52, 200, 334, 390, 330, 220],\r\n          animationDuration\r\n        }]\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;AAKA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAF,sBAAA,CAAAC,OAAA;;;;;;AADAA,OAAA;;AAGA,IAAAE,iBAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,MAAA,GAAAC,eAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAN,OAAA;IACA;IACAO,KAAA;MACAF,IAAA,EAAAC,MAAA;MACAN,OAAA;IACA;IACAQ,MAAA;MACAH,IAAA,EAAAC,MAAA;MACAN,OAAA;IACA;EACA;EACAS,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,SAAA;MACAD,KAAA,CAAAE,SAAA;IACA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,UAAAL,KAAA;MACA;IACA;IACA,KAAAA,KAAA,CAAAM,OAAA;IACA,KAAAN,KAAA;EACA;EACAO,OAAA;IACAH,SAAA,WAAAA,UAAA;MACA,KAAAJ,KAAA,GAAAQ,gBAAA,CAAAC,IAAA,MAAAC,GAAA;MAEA,KAAAV,KAAA,CAAAW,SAAA;QACAC,OAAA;UACAC,OAAA;UACAC,WAAA;YAAA;YACAnB,IAAA;UACA;QACA;QACAoB,IAAA;UACAC,GAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;UACAC,YAAA;QACA;QACAC,KAAA;UACA1B,IAAA;UACAI,IAAA;UACAuB,QAAA;YACAC,cAAA;UACA;QACA;QACAC,KAAA;UACA7B,IAAA;UACA2B,QAAA;YACAG,IAAA;UACA;QACA;QACAC,MAAA;UACAC,IAAA;UACAhC,IAAA;UACAiC,KAAA;UACAC,QAAA;UACA9B,IAAA;UACAZ,iBAAA,EAAAA;QACA;UACAwC,IAAA;UACAhC,IAAA;UACAiC,KAAA;UACAC,QAAA;UACA9B,IAAA;UACAZ,iBAAA,EAAAA;QACA;UACAwC,IAAA;UACAhC,IAAA;UACAiC,KAAA;UACAC,QAAA;UACA9B,IAAA;UACAZ,iBAAA,EAAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}