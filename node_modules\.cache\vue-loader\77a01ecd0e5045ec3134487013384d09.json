{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\easyCron\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\easyCron\\index.vue", "mtime": 1718070340305}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/easyCron", "sourcesContent": ["<template>\r\n  <Card class=\"easy-cron\">\r\n    <div class=\"content\">\r\n      <div class=\"left\">\r\n        <Tabs size=\"small\" v-model=\"curtab\">\r\n          <TabPane label=\"秒\" name=\"second\"><second-ui v-model=\"second\" :disabled=\"disabled\"></second-ui></TabPane>\r\n          <TabPane label=\"分\" name=\"minute\"><minute-ui v-model=\"minute\" :disabled=\"disabled\"></minute-ui></TabPane>\r\n          <TabPane label=\"时\" name=\"hour\"><hour-ui v-model=\"hour\" :disabled=\"disabled\"></hour-ui></TabPane>\r\n          <TabPane label=\"日\" name=\"day\"><day-ui v-model=\"day\" :week=\"week\" :disabled=\"disabled\"></day-ui></TabPane>\r\n          <TabPane label=\"月\" name=\"month\"><month-ui v-model=\"month\" :disabled=\"disabled\"></month-ui></TabPane>\r\n          <TabPane label=\"周\" name=\"week\"><week-ui v-model=\"week\" :day=\"day\" :disabled=\"disabled\"></week-ui></TabPane>\r\n          <TabPane label=\"年\" name=\"year\"><year-ui v-model=\"year\" :disabled=\"disabled\"></year-ui></TabPane>\r\n        </Tabs>\r\n      </div>\r\n      <div class=\"right\">\r\n        <div class=\"field-list\"><Table stripe :columns=\"columns\" :data=\"tableData\"\r\n          :show-header=\"false\" size=\"small\"></Table></div>\r\n        <div class=\"exe-pre\">\r\n          <div class=\"exe-pre-panel\">\r\n            <label class=\"p-left\">执行时间</label>\r\n            <DatePicker type=\"datetime\" v-model=\"startTime\" class=\"p-right\" @on-change=\"calTriggerList\"\r\n              placeholder=\"选择执行开始时间\"></DatePicker>\r\n          </div>\r\n          <div class=\"exe-pre-panel\">\r\n            <Tooltip content=\"执行预览解析不含年参数\" class=\"p-left\">\r\n              <label>执行预览</label>\r\n            </Tooltip>\r\n            <Input type=\"textarea\" :value=\"preTimeList\" class=\"p-right\" :rows=\"4\" readonly />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </Card>\r\n</template>\r\n\r\n<script>\r\nimport SecondUi from './tabs/second'\r\nimport MinuteUi from './tabs/minute'\r\nimport HourUi from './tabs/hour'\r\nimport DayUi from './tabs/day'\r\nimport WeekUi from './tabs/week'\r\nimport MonthUi from './tabs/month'\r\nimport YearUi from './tabs/year'\r\nimport CronParser from 'cron-parser'\r\nimport dateFormat from './format-date'\r\n\r\nexport default {\r\n  name: 'easy-cron',\r\n  model: {\r\n    prop: 'cronValue',\r\n    event: 'change'\r\n  },\r\n  props: {\r\n    cronValue: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data () {\r\n    return {\r\n      curtab: '',\r\n      second: '*',\r\n      minute: '*',\r\n      hour: '*',\r\n      day: '*',\r\n      month: '*',\r\n      week: '?',\r\n      year: '*',\r\n      startTime: new Date(),\r\n      preTimeList: '执行预览，会忽略年份参数',\r\n      columns: [\r\n        // {title: ' ', width: '80', key: 'name'}, {title: ' ', key: 'value', align: 'left'}\r\n        {title: ' ', width: '80', key: 'name'}\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    tableData () {\r\n      // return [\r\n      //   { name: '秒', value: this.second },\r\n      //   { name: '分', value: this.minute },\r\n      //   { name: '时', value: this.hour },\r\n      //   { name: '日', value: this.day },\r\n      //   { name: '月', value: this.month },\r\n      //   { name: '周', value: this.week },\r\n      //   { name: '年', value: this.year },\r\n      //   { name: '表达式', value: this.cronValue_c },\r\n      //   { name: '表达式(不含年)', value: this.cronValue_c2 }\r\n      // ]\r\n      return [\r\n        {name: '秒：' + this.second},\r\n        {name: '分：' + this.minute},\r\n        {name: '时：' + this.hour},\r\n        {name: '日：' + this.day},\r\n        {name: '月：' + this.month},\r\n        {name: '周：' + this.week},\r\n        {name: '年：' + this.year},\r\n        {name: '表达式：' + this.cronValue_c},\r\n        {name: '表达式(不含年)：' + this.cronValue_c2}\r\n      ]\r\n    },\r\n    cronValue_c () {\r\n      let result = []\r\n      result.push(this.second ? this.second : '*')\r\n      result.push(this.minute ? this.minute : '*')\r\n      result.push(this.hour ? this.hour : '*')\r\n      result.push(this.day ? this.day : '*')\r\n      result.push(this.month ? this.month : '*')\r\n      result.push(this.week ? this.week : '?')\r\n      result.push(this.year ? this.year : '*')\r\n      return result.join(' ')\r\n    },\r\n    cronValue_c2 () {\r\n      return this.cronValue_c.split(' ').slice(0, 6).join(' ')\r\n    }\r\n  },\r\n  watch: {\r\n    cronValue (newVal, oldVal) {\r\n      if (newVal === this.cronValue_c) {\r\n        // console.info('same cron value: ' + newVal)\r\n        return\r\n      }\r\n      this.formatValue()\r\n    },\r\n    cronValue_c (newVal, oldVal) {\r\n      this.calTriggerList()\r\n      this.$emit('change', newVal)\r\n    }\r\n  },\r\n  methods: {\r\n    formatValue () {\r\n      // console.info(this.cronValue)\r\n      if (!this.cronValue) return\r\n      const values = this.cronValue.split(' ').filter(item => !!item)\r\n      if (!values || values.length <= 0) return\r\n      this.second = values[0]\r\n      if (values.length > 1) this.minute = values[1]\r\n      if (values.length > 2) this.hour = values[2]\r\n      if (values.length > 3) this.day = values[3]\r\n      if (values.length > 4) this.month = values[4]\r\n      if (values.length > 5) this.week = values[5]\r\n      if (values.length > 6) this.year = values[6]\r\n    },\r\n    calTriggerList () {\r\n      // 去掉年份参数\r\n      const e = this.cronValue_c2\r\n      const format = 'yyyy-MM-dd hh:mm:ss'\r\n      const options = {\r\n        currentDate: dateFormat(this.startTime, format)\r\n      }\r\n      // console.info(options)\r\n      const iter = CronParser.parseExpression(e, options)\r\n      const result = []\r\n      for (let i = 0; i < 5; i++) {\r\n        result.push(dateFormat(new Date(iter.next()), format))\r\n      }\r\n      this.preTimeList = result.length > 0 ? result.join('\\n') : '无执行时间'\r\n    }\r\n  },\r\n  components: {\r\n    SecondUi,\r\n    MinuteUi,\r\n    HourUi,\r\n    DayUi,\r\n    WeekUi,\r\n    MonthUi,\r\n    YearUi\r\n  },\r\n  created () {\r\n    this.formatValue()\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.easy-cron {\r\n  display: inline-block;\r\n  border: 1px solid #1890ff;\r\n}\r\n\r\n.content {\r\n  display: flex;\r\n  flex-wrap: nowrap;\r\n}\r\n\r\n.left {\r\n  flex-basis: 60%;\r\n  width: 60%;\r\n  border: 1px solid transparent;\r\n  border-right-color: #1890ff;\r\n}\r\n\r\n.right {\r\n  flex-basis: 40%;\r\n  width: 40%;\r\n  /*border: 1px solid dimgray;*/\r\n}\r\n\r\n.ivu-table-small td {\r\n  height: 30px !important;\r\n}\r\n\r\n.exe-pre {\r\n  margin-top: 5px;\r\n}\r\n\r\n.exe-pre > div {\r\n  margin-top: 5px;\r\n}\r\n\r\n.exe-pre-panel {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n}\r\n\r\n.exe-pre-panel .p-left {\r\n  flex-basis: 80px;\r\n  flex-grow: 0;\r\n}\r\n\r\n.exe-pre-panel .p-right {\r\n  flex-basis: 100px;\r\n  flex-grow: 1;\r\n}\r\n</style>\r\n"]}]}