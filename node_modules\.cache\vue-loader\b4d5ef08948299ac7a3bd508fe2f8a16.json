{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\views\\components\\icons\\index.vue?vue&type=style&index=0&id=279234be&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\views\\components\\icons\\index.vue", "mtime": 1748224634823}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758071060223}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758071061987}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758071060880}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758071059610}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouaWNvbnMtY29udGFpbmVyIHsNCiAgbWFyZ2luOiAxMHB4IDIwcHggMDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCg0KICAuaWNvbi1pdGVtIHsNCiAgICBtYXJnaW46IDIwcHg7DQogICAgaGVpZ2h0OiA4NXB4Ow0KICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICB3aWR0aDogMTAwcHg7DQogICAgZmxvYXQ6IGxlZnQ7DQogICAgZm9udC1zaXplOiAzMHB4Ow0KICAgIGNvbG9yOiAjMjQyOTJlOw0KICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgfQ0KDQogIHNwYW4gew0KICAgIGRpc3BsYXk6IGJsb2NrOw0KICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICBtYXJnaW4tdG9wOiAxMHB4Ow0KICB9DQoNCiAgLmRpc2FibGVkIHsNCiAgICBwb2ludGVyLWV2ZW50czogbm9uZTsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6DA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/components/icons", "sourcesContent": ["<template>\r\n  <div class=\"icons-container\">\r\n    <aside>\r\n      <a href=\"#\" target=\"_blank\">Add and use\r\n      </a>\r\n    </aside>\r\n    <el-tabs type=\"border-card\">\r\n      <el-tab-pane label=\"Icons\">\r\n        <div v-for=\"item of svgIcons\" :key=\"item\">\r\n          <el-tooltip placement=\"top\">\r\n            <div slot=\"content\">\r\n              {{ generateIconCode(item) }}\r\n            </div>\r\n            <div class=\"icon-item\">\r\n              <svg-icon :icon-class=\"item\" class-name=\"disabled\" />\r\n              <span>{{ item }}</span>\r\n            </div>\r\n          </el-tooltip>\r\n        </div>\r\n      </el-tab-pane>\r\n      <el-tab-pane label=\"Element-UI Icons\">\r\n        <div v-for=\"item of elementIcons\" :key=\"item\">\r\n          <el-tooltip placement=\"top\">\r\n            <div slot=\"content\">\r\n              {{ generateElementIconCode(item) }}\r\n            </div>\r\n            <div class=\"icon-item\">\r\n              <i :class=\"'el-icon-' + item\" />\r\n              <span>{{ item }}</span>\r\n            </div>\r\n          </el-tooltip>\r\n        </div>\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport svgIcons from './svg-icons'\r\nimport elementIcons from './element-icons'\r\n\r\nexport default {\r\n  name: 'Icons',\r\n  data() {\r\n    return {\r\n      svgIcons,\r\n      elementIcons\r\n    }\r\n  },\r\n  methods: {\r\n    generateIconCode(symbol) {\r\n      return `<svg-icon icon-class=\"${symbol}\" />`\r\n    },\r\n    generateElementIconCode(symbol) {\r\n      return `<i class=\"el-icon-${symbol}\" />`\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.icons-container {\r\n  margin: 10px 20px 0;\r\n  overflow: hidden;\r\n\r\n  .icon-item {\r\n    margin: 20px;\r\n    height: 85px;\r\n    text-align: center;\r\n    width: 100px;\r\n    float: left;\r\n    font-size: 30px;\r\n    color: #24292e;\r\n    cursor: pointer;\r\n  }\r\n\r\n  span {\r\n    display: block;\r\n    font-size: 16px;\r\n    margin-top: 10px;\r\n  }\r\n\r\n  .disabled {\r\n    pointer-events: none;\r\n  }\r\n}\r\n</style>\r\n"]}]}