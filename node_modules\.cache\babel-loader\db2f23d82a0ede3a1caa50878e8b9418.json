{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project_about\\git174\\alznt-admin\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\project_about\\git174\\alznt-admin\\src\\components\\easyCron\\format-date.js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\easyCron\\format-date.js", "mtime": 1718070340305}, {"path": "D:\\project_about\\git174\\alznt-admin\\babel.config.js", "mtime": 1718238659557}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1758071059938}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLnRvLXN0cmluZy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnJlcGxhY2UuanMiKTsKdmFyIGRhdGVGb3JtYXQgPSBmdW5jdGlvbiBkYXRlRm9ybWF0KGRhdGUsIGJsb2NrKSB7CiAgaWYgKCFkYXRlKSB7CiAgICByZXR1cm4gJyc7CiAgfQogIHZhciBmb3JtYXQgPSBibG9jayB8fCAneXl5eS1NTS1kZCc7CiAgZGF0ZSA9IG5ldyBEYXRlKGRhdGUpOwogIHZhciBtYXAgPSB7CiAgICBNOiBkYXRlLmdldE1vbnRoKCkgKyAxLAogICAgLy8g5pyI5Lu9CiAgICBkOiBkYXRlLmdldERhdGUoKSwKICAgIC8vIOaXpQogICAgaDogZGF0ZS5nZXRIb3VycygpLAogICAgLy8g5bCP5pe2CiAgICBtOiBkYXRlLmdldE1pbnV0ZXMoKSwKICAgIC8vIOWIhgogICAgczogZGF0ZS5nZXRTZWNvbmRzKCksCiAgICAvLyDnp5IKICAgIHE6IE1hdGguZmxvb3IoKGRhdGUuZ2V0TW9udGgoKSArIDMpIC8gMyksCiAgICAvLyDlraPluqYKICAgIFM6IGRhdGUuZ2V0TWlsbGlzZWNvbmRzKCkgLy8g5q+r56eSCiAgfTsKICBmb3JtYXQgPSBmb3JtYXQucmVwbGFjZSgvKFt5TWRobXNxU10pKy9nLCBmdW5jdGlvbiAoYWxsLCB0KSB7CiAgICB2YXIgdiA9IG1hcFt0XTsKICAgIGlmICh2ICE9PSB1bmRlZmluZWQpIHsKICAgICAgaWYgKGFsbC5sZW5ndGggPiAxKSB7CiAgICAgICAgdiA9ICIwIi5jb25jYXQodik7CiAgICAgICAgdiA9IHYuc3Vic3RyKHYubGVuZ3RoIC0gMik7CiAgICAgIH0KICAgICAgcmV0dXJuIHY7CiAgICB9IGVsc2UgaWYgKHQgPT09ICd5JykgewogICAgICByZXR1cm4gZGF0ZS5nZXRGdWxsWWVhcigpLnRvU3RyaW5nKCkuc3Vic3RyKDQgLSBhbGwubGVuZ3RoKTsKICAgIH0KICAgIHJldHVybiBhbGw7CiAgfSk7CiAgcmV0dXJuIGZvcm1hdDsKfTsKdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gZGF0ZUZvcm1hdDs="}, {"version": 3, "names": ["dateFormat", "date", "block", "format", "Date", "map", "M", "getMonth", "d", "getDate", "h", "getHours", "m", "getMinutes", "s", "getSeconds", "q", "Math", "floor", "S", "getMilliseconds", "replace", "all", "t", "v", "undefined", "length", "concat", "substr", "getFullYear", "toString", "_default", "exports", "default"], "sources": ["D:/project_about/git174/alznt-admin/src/components/easyCron/format-date.js"], "sourcesContent": ["const dateFormat = (date, block) => {\r\n  if (!date) {\r\n    return ''\r\n  }\r\n\r\n  let format = block || 'yyyy-MM-dd'\r\n\r\n  date = new Date(date)\r\n\r\n  const map = {\r\n    M: date.getMonth() + 1, // 月份\r\n    d: date.getDate(), // 日\r\n    h: date.getHours(), // 小时\r\n    m: date.getMinutes(), // 分\r\n    s: date.getSeconds(), // 秒\r\n    q: Math.floor((date.getMonth() + 3) / 3), // 季度\r\n    S: date.getMilliseconds() // 毫秒\r\n  }\r\n\r\n  format = format.replace(/([yMdhmsqS])+/g, (all, t) => {\r\n    let v = map[t]\r\n    if (v !== undefined) {\r\n      if (all.length > 1) {\r\n        v = `0${v}`\r\n        v = v.substr(v.length - 2)\r\n      }\r\n      return v\r\n    } else if (t === 'y') {\r\n      return (date.getFullYear().toString()).substr(4 - all.length)\r\n    }\r\n    return all\r\n  })\r\n\r\n  return format\r\n}\r\n\r\nexport default dateFormat\r\n"], "mappings": ";;;;;;;;;;AAAA,IAAMA,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAAI,EAAEC,KAAK,EAAK;EAClC,IAAI,CAACD,IAAI,EAAE;IACT,OAAO,EAAE;EACX;EAEA,IAAIE,MAAM,GAAGD,KAAK,IAAI,YAAY;EAElCD,IAAI,GAAG,IAAIG,IAAI,CAACH,IAAI,CAAC;EAErB,IAAMI,GAAG,GAAG;IACVC,CAAC,EAAEL,IAAI,CAACM,QAAQ,CAAC,CAAC,GAAG,CAAC;IAAE;IACxBC,CAAC,EAAEP,IAAI,CAACQ,OAAO,CAAC,CAAC;IAAE;IACnBC,CAAC,EAAET,IAAI,CAACU,QAAQ,CAAC,CAAC;IAAE;IACpBC,CAAC,EAAEX,IAAI,CAACY,UAAU,CAAC,CAAC;IAAE;IACtBC,CAAC,EAAEb,IAAI,CAACc,UAAU,CAAC,CAAC;IAAE;IACtBC,CAAC,EAAEC,IAAI,CAACC,KAAK,CAAC,CAACjB,IAAI,CAACM,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAAE;IAC1CY,CAAC,EAAElB,IAAI,CAACmB,eAAe,CAAC,CAAC,CAAC;EAC5B,CAAC;EAEDjB,MAAM,GAAGA,MAAM,CAACkB,OAAO,CAAC,gBAAgB,EAAE,UAACC,GAAG,EAAEC,CAAC,EAAK;IACpD,IAAIC,CAAC,GAAGnB,GAAG,CAACkB,CAAC,CAAC;IACd,IAAIC,CAAC,KAAKC,SAAS,EAAE;MACnB,IAAIH,GAAG,CAACI,MAAM,GAAG,CAAC,EAAE;QAClBF,CAAC,OAAAG,MAAA,CAAOH,CAAC,CAAE;QACXA,CAAC,GAAGA,CAAC,CAACI,MAAM,CAACJ,CAAC,CAACE,MAAM,GAAG,CAAC,CAAC;MAC5B;MACA,OAAOF,CAAC;IACV,CAAC,MAAM,IAAID,CAAC,KAAK,GAAG,EAAE;MACpB,OAAQtB,IAAI,CAAC4B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAEF,MAAM,CAAC,CAAC,GAAGN,GAAG,CAACI,MAAM,CAAC;IAC/D;IACA,OAAOJ,GAAG;EACZ,CAAC,CAAC;EAEF,OAAOnB,MAAM;AACf,CAAC;AAAA,IAAA4B,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEcjC,UAAU", "ignoreList": []}]}