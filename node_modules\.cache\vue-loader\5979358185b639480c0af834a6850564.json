{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\easyCron\\tabs\\year.vue?vue&type=style&index=0&id=0e33c51f&scoped=true&lang=css", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\easyCron\\tabs\\year.vue", "mtime": 1718070340308}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758071060223}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758071061987}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758071060880}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoNCi5jb25maWctbGlzdCB7DQogIHRleHQtYWxpZ246IGxlZnQ7DQogIG1hcmdpbjogMCAxMHB4IDEwcHggMTBweDsNCn0NCg0KLml0ZW0gew0KICBtYXJnaW4tdG9wOiA1cHg7DQp9DQoNCi5jaG9pY2Ugew0KICBib3JkZXI6IDFweCBzb2xpZCB0cmFuc3BhcmVudDsNCiAgcGFkZGluZzogNXB4IDhweDsNCn0NCg0KLmNob2ljZTpob3ZlciB7DQogIGJvcmRlcjogMXB4IHNvbGlkICMxODkwZmY7DQp9DQoNCi53NjAgew0KICB3aWR0aDogNjBweDsNCn0NCg0KLml2dS1pbnB1dC1udW1iZXIgew0KICBtYXJnaW4tbGVmdDogNXB4Ow0KICBtYXJnaW4tcmlnaHQ6IDVweDsNCn0NCg0KLmxpc3Qgew0KICBtYXJnaW46IDAgMjBweDsNCn0NCg0KLmxpc3QtY2hlY2staXRlbSB7DQogIHBhZGRpbmc6IDFweCAzcHg7DQogIHdpZHRoOiA0ZW07DQp9DQo="}, {"version": 3, "sources": ["year.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwDA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "year.vue", "sourceRoot": "src/components/easyCron/tabs", "sourcesContent": ["<template>\r\n  <div class=\"config-list\">\r\n    <RadioGroup v-model=\"type\">\r\n    <div class=\"item\">\r\n      <Radio label=\"TYPE_EVERY\" class=\"choice\" :disabled=\"disabled\">每年</Radio>\r\n    </div>\r\n    <div class=\"item\">\r\n      <Radio label=\"TYPE_RANGE\" class=\"choice\" :disabled=\"disabled\">区间</Radio>\r\n       从<InputNumber :disabled=\"type!=TYPE_RANGE || disabled\" :min=\"0\" :precision=\"0\"\r\n        class=\"w60\" v-model=\"valueRange.start\" />年\r\n       至<InputNumber :disabled=\"type!=TYPE_RANGE || disabled\" :min=\"1\" :precision=\"0\"\r\n        class=\"w60\" v-model=\"valueRange.end\" />年\r\n    </div>\r\n    <div class=\"item\">\r\n      <Radio label=\"TYPE_LOOP\" class=\"choice\" :disabled=\"disabled\">循环</Radio>\r\n      从<InputNumber :disabled=\"type!=TYPE_LOOP || disabled\" :min=\"0\" :precision=\"0\"\r\n       class=\"w60\" v-model=\"valueLoop.start\" />年开始，间隔\r\n      <InputNumber :disabled=\"type!=TYPE_LOOP || disabled\" :min=\"1\" :precision=\"0\"\r\n       class=\"w60\" v-model=\"valueLoop.interval\" />年\r\n    </div>\r\n    </RadioGroup>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport mixin from './mixin'\r\n\r\nexport default {\r\n  name: 'year',\r\n  mixins: [mixin],\r\n  data () {\r\n    return {}\r\n  },\r\n  watch: {\r\n    value_c (newVal, oldVal) {\r\n      // console.info('change:' + newVal)\r\n      this.$emit('change', newVal)\r\n    }\r\n  },\r\n  created () {\r\n    const nowYear = (new Date()).getFullYear()\r\n    this.DEFAULT_VALUE = '*'\r\n    this.minValue = 0\r\n    this.maxValue = 0\r\n    this.valueRange.start = nowYear\r\n    this.valueRange.end = nowYear + 100\r\n    this.valueLoop.start = nowYear\r\n    this.valueLoop.interval = 1\r\n    // console.info('created')\r\n    this.parseProp(this.prop)\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n.config-list {\r\n  text-align: left;\r\n  margin: 0 10px 10px 10px;\r\n}\r\n\r\n.item {\r\n  margin-top: 5px;\r\n}\r\n\r\n.choice {\r\n  border: 1px solid transparent;\r\n  padding: 5px 8px;\r\n}\r\n\r\n.choice:hover {\r\n  border: 1px solid #1890ff;\r\n}\r\n\r\n.w60 {\r\n  width: 60px;\r\n}\r\n\r\n.ivu-input-number {\r\n  margin-left: 5px;\r\n  margin-right: 5px;\r\n}\r\n\r\n.list {\r\n  margin: 0 20px;\r\n}\r\n\r\n.list-check-item {\r\n  padding: 1px 3px;\r\n  width: 4em;\r\n}\r\n</style>\r\n"]}]}