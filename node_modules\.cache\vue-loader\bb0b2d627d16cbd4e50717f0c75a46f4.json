{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\views\\dashboard\\BarChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\views\\dashboard\\BarChart.vue", "mtime": 1718070340323}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["BarChart.vue"], "names": [], "mappings": ";;;;;AAKA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BarChart.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\r\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\r\n</template>\r\n\r\n<script>\r\nimport echarts from 'echarts'\r\nrequire('echarts/theme/macarons') // echarts theme\r\nimport resize from './mixins/resize'\r\n\r\nconst animationDuration = 6000\r\n\r\nexport default {\r\n  mixins: [resize],\r\n  props: {\r\n    className: {\r\n      type: String,\r\n      default: 'chart'\r\n    },\r\n    width: {\r\n      type: String,\r\n      default: '100%'\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '300px'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chart: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initChart()\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    if (!this.chart) {\r\n      return\r\n    }\r\n    this.chart.dispose()\r\n    this.chart = null\r\n  },\r\n  methods: {\r\n    initChart() {\r\n      this.chart = echarts.init(this.$el, 'macarons')\r\n\r\n      this.chart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { // 坐标轴指示器，坐标轴触发有效\r\n            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'\r\n          }\r\n        },\r\n        grid: {\r\n          top: 10,\r\n          left: '2%',\r\n          right: '2%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: [{\r\n          type: 'category',\r\n          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\r\n          axisTick: {\r\n            alignWithLabel: true\r\n          }\r\n        }],\r\n        yAxis: [{\r\n          type: 'value',\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        }],\r\n        series: [{\r\n          name: 'pageA',\r\n          type: 'bar',\r\n          stack: 'vistors',\r\n          barWidth: '60%',\r\n          data: [79, 52, 200, 334, 390, 330, 220],\r\n          animationDuration\r\n        }, {\r\n          name: 'pageB',\r\n          type: 'bar',\r\n          stack: 'vistors',\r\n          barWidth: '60%',\r\n          data: [80, 52, 200, 334, 390, 330, 220],\r\n          animationDuration\r\n        }, {\r\n          name: 'pageC',\r\n          type: 'bar',\r\n          stack: 'vistors',\r\n          barWidth: '60%',\r\n          data: [30, 52, 200, 334, 390, 330, 220],\r\n          animationDuration\r\n        }]\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}