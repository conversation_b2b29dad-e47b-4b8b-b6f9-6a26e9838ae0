{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\views\\login.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\views\\login.vue", "mtime": 1744789047407}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "login.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"login\">\r\n    <el-form\r\n      v-if=\"showLoginForm\"\r\n      ref=\"loginForm\"\r\n      :model=\"loginForm\"\r\n      :rules=\"loginRules\"\r\n      class=\"login-form\"\r\n    >\r\n      <h3 class=\"title\">开发区一网统管后台管理系统</h3>\r\n      <el-form-item prop=\"username\">\r\n        <el-input\r\n          v-model=\"loginForm.username\"\r\n          type=\"text\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"账号\"\r\n        >\r\n          <svg-icon\r\n            slot=\"prefix\"\r\n            icon-class=\"user\"\r\n            class=\"el-input__icon input-icon\"\r\n          />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"password\">\r\n        <el-input\r\n          v-model=\"loginForm.password\"\r\n          type=\"password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"密码\"\r\n          @keyup.enter.native=\"handleLogin\"\r\n        >\r\n          <svg-icon\r\n            slot=\"prefix\"\r\n            icon-class=\"password\"\r\n            class=\"el-input__icon input-icon\"\r\n          />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"code\">\r\n        <el-input\r\n          v-model=\"loginForm.code\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"验证码\"\r\n          style=\"width: 63%\"\r\n          @keyup.enter.native=\"handleLogin\"\r\n        >\r\n          <svg-icon\r\n            slot=\"prefix\"\r\n            icon-class=\"validCode\"\r\n            class=\"el-input__icon input-icon\"\r\n          />\r\n        </el-input>\r\n        <div class=\"login-code\">\r\n          <img :src=\"codeUrl\" @click=\"getCode\" class=\"login-code-img\" />\r\n        </div>\r\n      </el-form-item>\r\n      <el-checkbox\r\n        v-model=\"loginForm.rememberMe\"\r\n        style=\"margin: 0px 0px 25px 0px\"\r\n        >记住密码</el-checkbox\r\n      >\r\n      <el-form-item style=\"width: 100%\">\r\n        <el-button\r\n          :loading=\"loading\"\r\n          size=\"medium\"\r\n          type=\"primary\"\r\n          style=\"width: 100%\"\r\n          @click.native.prevent=\"handleLogin\"\r\n        >\r\n          <span v-if=\"!loading\">登 录</span>\r\n          <span v-else>登 录 中...</span>\r\n        </el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n    <div class=\"login_box\" v-else>\r\n      <h3 class=\"title\">开发区一网统管后台管理系统</h3>\r\n      <iframe\r\n        id=\"mainIframe\"\r\n        style=\"padding: 25px; padding-bottom: 5px\"\r\n        width=\"400px\"\r\n        height=\"350px\"\r\n        frameborder=\"0\"\r\n        scrolling=\"0\"\r\n        :src=\"scanUrl\"\r\n        sandbox=\"allow-scripts allow-top-navigation allow-same-origin\"\r\n      ></iframe>\r\n    </div>\r\n    <!--  底部  -->\r\n    <div class=\"el-login-footer\">\r\n      <span>Copyright © 2018-2021 ruoyi.vip All Rights Reserved.</span>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCodeImg } from \"@/api/login\";\r\nimport Cookies from \"js-cookie\";\r\nimport { encrypt, decrypt } from \"@/utils/jsencrypt\";\r\n\r\nexport default {\r\n  name: \"Login\",\r\n  data() {\r\n    return {\r\n      showLoginForm: false,\r\n      scanUrl: `${process.env.VUE_APP_SAOMA_CODE}/oauth2/auth.htm?response_type=code&client_id=${process.env.VUE_APP_CLIENT_ID}&redirect_uri=${process.env.VUE_APP_REDIRECT_URI}&scope=get_user_info&authType=QRCODE&embedMode=true`,\r\n      codeUrl: \"\",\r\n      cookiePassword: \"\",\r\n      loginForm: {\r\n        username: \"admin\",\r\n        password: \"admin123\",\r\n        rememberMe: false,\r\n        code: \"\",\r\n        uuid: \"\",\r\n      },\r\n      loginRules: {\r\n        username: [\r\n          { required: true, trigger: \"blur\", message: \"用户名不能为空\" },\r\n        ],\r\n        password: [\r\n          { required: true, trigger: \"blur\", message: \"密码不能为空\" },\r\n        ],\r\n        code: [\r\n          { required: true, trigger: \"change\", message: \"验证码不能为空\" },\r\n        ],\r\n      },\r\n      loading: false,\r\n      redirect: undefined,\r\n    };\r\n  },\r\n  watch: {\r\n    $route: {\r\n      handler: function (route) {\r\n        console.log(\"route\", route);\r\n        this.showLoginForm = route.fullPath.includes(\"user\");\r\n      },\r\n      immediate: true,\r\n    },\r\n  },\r\n  created() {\r\n    this.getCode();\r\n    this.getCookie();\r\n  },\r\n  mounted() {\r\n    window.addEventListener(\"message\", this.loginZZD);\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener(\"message\", this.loginZZD);\r\n  },\r\n  methods: {\r\n    // 浙政钉登录\r\n    loginZZD(event) {\r\n      const code = event.data && event.data.code;\r\n      console.log(\"code\", event);\r\n      if (code) {\r\n        console.log(\"code1111\", code);\r\n        this.$store\r\n          .dispatch(\"Login\", { zzdCode: code })\r\n          .then(() => {\r\n            this.$router.push({ path: this.redirect || \"/\" }).catch(() => {});\r\n          })\r\n          .catch(() => {\r\n            this.loading = false;\r\n            this.getCode();\r\n          });\r\n      }\r\n    },\r\n    getCode() {\r\n      getCodeImg().then((res) => {\r\n        this.codeUrl = \"data:image/gif;base64,\" + res.img;\r\n        this.loginForm.uuid = res.uuid;\r\n      });\r\n    },\r\n    getCookie() {\r\n      const username = Cookies.get(\"username\");\r\n      const password = Cookies.get(\"password\");\r\n      const rememberMe = Cookies.get(\"rememberMe\");\r\n      this.loginForm = {\r\n        username: username === undefined ? this.loginForm.username : username,\r\n        password:\r\n          password === undefined ? this.loginForm.password : decrypt(password),\r\n        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),\r\n      };\r\n    },\r\n    handleLogin() {\r\n      this.$refs.loginForm.validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          if (this.loginForm.rememberMe) {\r\n            Cookies.set(\"username\", this.loginForm.username, { expires: 30 });\r\n            Cookies.set(\"password\", encrypt(this.loginForm.password), {\r\n              expires: 30,\r\n            });\r\n            Cookies.set(\"rememberMe\", this.loginForm.rememberMe, {\r\n              expires: 30,\r\n            });\r\n          } else {\r\n            Cookies.remove(\"username\");\r\n            Cookies.remove(\"password\");\r\n            Cookies.remove(\"rememberMe\");\r\n          }\r\n          this.loginForm.password = encrypt(this.loginForm.password);\r\n          this.$store\r\n            .dispatch(\"Login\", this.loginForm)\r\n            .then(() => {\r\n              this.$router.push({ path: this.redirect || \"/\" }).catch(() => {});\r\n            })\r\n            .catch(() => {\r\n              this.loading = false;\r\n              this.getCode();\r\n            });\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\">\r\n.login {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  background-image: url(\"../assets/images/login-background.jpg\");\r\n  background-size: cover;\r\n}\r\n.title {\r\n  margin: 0px auto 30px auto;\r\n  text-align: center;\r\n  color: #707070;\r\n}\r\n\r\n.login-form {\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  width: 400px;\r\n  padding: 25px 25px 5px 25px;\r\n  .el-input {\r\n    height: 38px;\r\n    input {\r\n      height: 38px;\r\n    }\r\n  }\r\n  .input-icon {\r\n    height: 39px;\r\n    width: 14px;\r\n    margin-left: 2px;\r\n  }\r\n}\r\n.login-tip {\r\n  font-size: 13px;\r\n  text-align: center;\r\n  color: #bfbfbf;\r\n}\r\n.login-code {\r\n  width: 33%;\r\n  height: 38px;\r\n  float: right;\r\n  img {\r\n    cursor: pointer;\r\n    vertical-align: middle;\r\n  }\r\n}\r\n.el-login-footer {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  position: fixed;\r\n  bottom: 0;\r\n  width: 100%;\r\n  text-align: center;\r\n  color: #fff;\r\n  font-family: Arial;\r\n  font-size: 12px;\r\n  letter-spacing: 1px;\r\n}\r\n.login-code-img {\r\n  height: 38px;\r\n}\r\n.login_box {\r\n  background: #fff;\r\n  padding-top: 24px;\r\n  .title {\r\n    margin: 0px auto;\r\n    text-align: center;\r\n    color: #707070;\r\n  }\r\n}\r\n</style>\r\n"]}]}