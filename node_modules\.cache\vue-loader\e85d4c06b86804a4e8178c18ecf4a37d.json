{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\views\\dashboard\\LineChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\views\\dashboard\\LineChart.vue", "mtime": 1718070340323}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["LineChart.vue"], "names": [], "mappings": ";;;;;AAKA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "LineChart.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\r\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\r\n</template>\r\n\r\n<script>\r\nimport echarts from 'echarts'\r\nrequire('echarts/theme/macarons') // echarts theme\r\nimport resize from './mixins/resize'\r\n\r\nexport default {\r\n  mixins: [resize],\r\n  props: {\r\n    className: {\r\n      type: String,\r\n      default: 'chart'\r\n    },\r\n    width: {\r\n      type: String,\r\n      default: '100%'\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '350px'\r\n    },\r\n    autoResize: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chartData: {\r\n      type: Object,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chart: null\r\n    }\r\n  },\r\n  watch: {\r\n    chartData: {\r\n      deep: true,\r\n      handler(val) {\r\n        this.setOptions(val)\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initChart()\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    if (!this.chart) {\r\n      return\r\n    }\r\n    this.chart.dispose()\r\n    this.chart = null\r\n  },\r\n  methods: {\r\n    initChart() {\r\n      this.chart = echarts.init(this.$el, 'macarons')\r\n      this.setOptions(this.chartData)\r\n    },\r\n    setOptions({ expectedData, actualData } = {}) {\r\n      this.chart.setOption({\r\n        xAxis: {\r\n          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\r\n          boundaryGap: false,\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        },\r\n        grid: {\r\n          left: 10,\r\n          right: 10,\r\n          bottom: 20,\r\n          top: 30,\r\n          containLabel: true\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross'\r\n          },\r\n          padding: [5, 10]\r\n        },\r\n        yAxis: {\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        },\r\n        legend: {\r\n          data: ['expected', 'actual']\r\n        },\r\n        series: [{\r\n          name: 'expected', itemStyle: {\r\n            normal: {\r\n              color: '#FF005A',\r\n              lineStyle: {\r\n                color: '#FF005A',\r\n                width: 2\r\n              }\r\n            }\r\n          },\r\n          smooth: true,\r\n          type: 'line',\r\n          data: expectedData,\r\n          animationDuration: 2800,\r\n          animationEasing: 'cubicInOut'\r\n        },\r\n        {\r\n          name: 'actual',\r\n          smooth: true,\r\n          type: 'line',\r\n          itemStyle: {\r\n            normal: {\r\n              color: '#3888fa',\r\n              lineStyle: {\r\n                color: '#3888fa',\r\n                width: 2\r\n              },\r\n              areaStyle: {\r\n                color: '#f3f8ff'\r\n              }\r\n            }\r\n          },\r\n          data: actualData,\r\n          animationDuration: 2800,\r\n          animationEasing: 'quadraticOut'\r\n        }]\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}