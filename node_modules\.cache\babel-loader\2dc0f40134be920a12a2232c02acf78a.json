{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\layout\\components\\Sidebar\\SidebarItem.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\layout\\components\\Sidebar\\SidebarItem.vue", "mtime": 1718070340312}, {"path": "D:\\project_about\\git174\\alznt-admin\\babel.config.js", "mtime": 1718238659557}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_path", "_interopRequireDefault", "require", "_validate", "_Item", "_Link", "_FixiOSBug", "name", "components", "<PERSON><PERSON>", "AppLink", "mixins", "FixiOSBug", "props", "item", "type", "Object", "required", "isNest", "Boolean", "default", "basePath", "String", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "methods", "hasOneShowingChild", "_this", "children", "arguments", "length", "undefined", "parent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "hidden", "_objectSpread2", "path", "noShowingChildren", "<PERSON><PERSON><PERSON>", "routePath", "isExternal", "resolve"], "sources": ["src/layout/components/Sidebar/SidebarItem.vue"], "sourcesContent": ["<template>\r\n  <div v-if=\"!item.hidden\">\r\n    <template v-if=\"hasOneShowingChild(item.children,item) && (!onlyOneChild.children||onlyOneChild.noShowingChildren)&&!item.alwaysShow\">\r\n      <app-link v-if=\"onlyOneChild.meta\" :to=\"resolvePath(onlyOneChild.path)\">\r\n        <el-menu-item :index=\"resolvePath(onlyOneChild.path)\" :class=\"{'submenu-title-noDropdown':!isNest}\">\r\n          <item :icon=\"onlyOneChild.meta.icon||(item.meta&&item.meta.icon)\" :title=\"onlyOneChild.meta.title\" />\r\n        </el-menu-item>\r\n      </app-link>\r\n    </template>\r\n\r\n    <el-submenu v-else ref=\"subMenu\" :index=\"resolvePath(item.path)\" popper-append-to-body>\r\n      <template slot=\"title\">\r\n        <item v-if=\"item.meta\" :icon=\"item.meta && item.meta.icon\" :title=\"item.meta.title\" />\r\n      </template>\r\n      <sidebar-item\r\n        v-for=\"child in item.children\"\r\n        :key=\"child.path\"\r\n        :is-nest=\"true\"\r\n        :item=\"child\"\r\n        :base-path=\"resolvePath(child.path)\"\r\n        class=\"nest-menu\"\r\n      />\r\n    </el-submenu>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport path from 'path'\r\nimport { isExternal } from '@/utils/validate'\r\nimport Item from './Item'\r\nimport AppLink from './Link'\r\nimport FixiOSBug from './FixiOSBug'\r\n\r\nexport default {\r\n  name: 'SidebarItem',\r\n  components: { Item, AppLink },\r\n  mixins: [FixiOSBug],\r\n  props: {\r\n    // route object\r\n    item: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    isNest: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    basePath: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    this.onlyOneChild = null\r\n    return {}\r\n  },\r\n  methods: {\r\n    hasOneShowingChild(children = [], parent) {\r\n      if (!children) {\r\n        children = [];\r\n      }\r\n      const showingChildren = children.filter(item => {\r\n        if (item.hidden) {\r\n          return false\r\n        } else {\r\n          // Temp set(will be used if only has one showing child)\r\n          this.onlyOneChild = item\r\n          return true\r\n        }\r\n      })\r\n\r\n      // When there is only one child router, the child router is displayed by default\r\n      if (showingChildren.length === 1) {\r\n        return true\r\n      }\r\n\r\n      // Show parent if there are no child router to display\r\n      if (showingChildren.length === 0) {\r\n        this.onlyOneChild = { ... parent, path: '', noShowingChildren: true }\r\n        return true\r\n      }\r\n\r\n      return false\r\n    },\r\n    resolvePath(routePath) {\r\n      if (isExternal(routePath)) {\r\n        return routePath\r\n      }\r\n      if (isExternal(this.basePath)) {\r\n        return this.basePath\r\n      }\r\n      return path.resolve(this.basePath, routePath)\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;AA2BA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,KAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,UAAA,GAAAL,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAK,IAAA;EACAC,UAAA;IAAAC,IAAA,EAAAA,aAAA;IAAAC,OAAA,EAAAA;EAAA;EACAC,MAAA,GAAAC,kBAAA;EACAC,KAAA;IACA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,MAAA;MACAH,IAAA,EAAAI,OAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACAN,IAAA,EAAAO,MAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA,KAAAC,YAAA;IACA;EACA;EACAC,OAAA;IACAC,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,KAAA;MAAA,IAAAC,QAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MAAA,IAAAG,MAAA,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;MACA,KAAAH,QAAA;QACAA,QAAA;MACA;MACA,IAAAK,eAAA,GAAAL,QAAA,CAAAM,MAAA,WAAApB,IAAA;QACA,IAAAA,IAAA,CAAAqB,MAAA;UACA;QACA;UACA;UACAR,KAAA,CAAAH,YAAA,GAAAV,IAAA;UACA;QACA;MACA;;MAEA;MACA,IAAAmB,eAAA,CAAAH,MAAA;QACA;MACA;;MAEA;MACA,IAAAG,eAAA,CAAAH,MAAA;QACA,KAAAN,YAAA,OAAAY,cAAA,CAAAhB,OAAA,MAAAgB,cAAA,CAAAhB,OAAA,MAAAY,MAAA;UAAAK,IAAA;UAAAC,iBAAA;QAAA;QACA;MACA;MAEA;IACA;IACAC,WAAA,WAAAA,YAAAC,SAAA;MACA,QAAAC,oBAAA,EAAAD,SAAA;QACA,OAAAA,SAAA;MACA;MACA,QAAAC,oBAAA,OAAApB,QAAA;QACA,YAAAA,QAAA;MACA;MACA,OAAAgB,aAAA,CAAAK,OAAA,MAAArB,QAAA,EAAAmB,SAAA;IACA;EACA;AACA", "ignoreList": []}]}