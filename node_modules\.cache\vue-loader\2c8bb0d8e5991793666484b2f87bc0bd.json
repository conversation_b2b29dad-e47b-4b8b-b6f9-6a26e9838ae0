{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\layout\\components\\Sidebar\\Logo.vue?vue&type=template&id=6494804b&scoped=true", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\layout\\components\\Sidebar\\Logo.vue", "mtime": 1718070340312}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1758071062035}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}