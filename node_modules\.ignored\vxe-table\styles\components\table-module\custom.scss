@use '../../helpers/baseMixin.scss';

.vxe-table-custom--option {
  position: relative;
  display: flex;
  flex-direction: row;
  &.active--drag-origin {
    opacity: 0.5;
  }
  &.active--drag-target {
    &[drag-pos="top"] {
      &::after {
        display: block;
        top: -2px;
      }
    }
    &[drag-pos="bottom"] {
      &::after {
        display: block;
        bottom: -2px;
      }
    }
  }
  &:first-child {
    &[drag-pos="top"] {
      &::after {
        top: 0;
      }
    }
  }
  &:last-child {
    &[drag-pos="bottom"] {
      &::after {
        bottom: 0;
      }
    }
  }
  &::after {
    display: none;
    content: "";
    position: absolute;
    left: -1px;
    width: calc(100% + 1px);
    height: 2px;
    background-color: var(--vxe-ui-font-primary-color);
    z-index: 12;
  }
  &:last-child {
    &::after {
      width: 100%;
    }
  }
}

.vxe-table-custom--handle-wrapper {
  display: flex;
  flex-direction: column;
}

.vxe-table-custom-wrapper {
  display: none;
  flex-direction: row;
  position: absolute;
  text-align: left;
  background-color: var(--vxe-ui-layout-background-color);
  z-index: 19;
  border: 1px solid var(--vxe-ui-table-border-color);
  border-radius: var(--vxe-ui-border-radius);
  box-shadow: var(--vxe-ui-base-popup-box-shadow);
  &.placement--top-left {
    top: 2px;
    left: 2px;
  }
  &.placement--top-right {
    top: 2px;
    right: 2px;
  }
  &.placement--bottom-left {
    bottom: 2px;
    left: 2px;
  }
  &.placement--bottom-right {
    bottom: 2px;
    right: 2px;
  }
  &.placement--left {
    left: 2px;
  }
  &.placement--right {
    right: 2px;
  }
  &.placement--left,
  &.placement--right {
    top: 2px;
    height: calc(100% - 4px);
  }
  &.is--active {
    display: flex;
  }
}


.vxe-table-custom-simple--body-wrapper {
  position: relative;
  flex-grow: 1;
  display: flex;
  flex-direction: row;
}

.vxe-table-custom--body {
  position: relative;
  display: block;
  flex-grow: 1;
  overflow-x: hidden;
  overflow-y: auto;
}

.vxe-table-custom--panel-list {
  list-style-type: none;
  margin: 0;
  padding: 0;
  & > li {
    max-width: 26em;
    min-width: 18em;
    padding: 0.2em 1em 0.2em 1em;
    @for $i from 2 through 8 {
      $interval: $i - 1 + 0.5;
      &.level--#{$i}{
        padding-left: #{$interval + 1.2}em;
      }
    }
  }
}

.vxe-table-custom--header {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  height: 2.4em;
  font-weight: 700;
  border-bottom: 1px solid var(--vxe-ui-base-popup-border-color);
  user-select: none;
}

.vxe-table-custom--panel-list {
  .vxe-table-custom--checkbox-option {
    &:hover {
      background-color: var(--vxe-ui-table-row-hover-background-color);
    }
  }
}

.vxe-table-custom--footer-buttons {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-shrink: 0;
  border-top: 1px solid var(--vxe-ui-base-popup-border-color);
  button {
    flex-grow: 1;
    height: 2.8em;
  }
}

.vxe-table-custom--checkbox-option {
  @include baseMixin.createCheckboxIcon();
}
.vxe-table-custom--checkbox-option,
.vxe-table-custom--sort-option {
  padding-right: 0.4em;
  flex-shrink: 0;
  user-select: none;
}
.vxe-table-custom--sort-option {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.vxe-table-custom--sort-btn {
  padding-left: 0.2em;
  padding-right: 0.4em;
  &:not(.is--disabled) {
    cursor: grab;
    &:active {
      cursor: grabbing;
    }
    &:hover {
      color: var(--vxe-ui-font-primary-color);
    }
  }
  &.is--disabled {
    color: var(--vxe-ui-input-disabled-color);
    cursor: not-allowed;
  }
}
.vxe-table-custom--name-option {
  flex-grow: 1;
  display: flex;
  flex-direction: row;
  overflow: hidden;
}
.vxe-table-custom--checkbox-label  {
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.vxe-table-custom--fixed-option {
  flex-shrink: 0;
  padding-left: 0.5em;
  display: flex;
  flex-direction: row;
  align-items: center;
  user-select: none;
}

.vxe-table-custom-popup--body-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  outline: 0;
}
.vxe-table-custom-popup--handle-wrapper {
  flex-grow: 1;
  outline: 0;
  overflow: auto;
}

.vxe-table-custom-popup--table-wrapper {
  border-bottom: 1px solid var(--vxe-ui-table-border-color);
  table {
    width: 100%;
    border: 0;
    table-layout: fixed;
    word-break: break-all;
    border-spacing: 0;
    border-collapse: separate;
    th {
      position: sticky;
      top: 0;
      text-align: left;
      border-top: 1px solid var(--vxe-ui-table-border-color);
      border-bottom: 1px solid var(--vxe-ui-table-border-color);
      border-left: 1px solid var(--vxe-ui-table-border-color);
      background-color: var(--vxe-ui-table-header-background-color);
      z-index: 7;
    }
    td {
      border-top: 1px solid var(--vxe-ui-table-border-color);
      border-left: 1px solid var(--vxe-ui-table-border-color);
    }
    tr {
      &:first-child {
        td {
          border-top: 0;
        }
      }
      &:hover {
        background-color: var(--vxe-ui-table-row-hover-background-color);
      }
    }
    th,
    td {
      height: 44px;
      padding: 0 0.6em;
      &:last-child {
        border-right: 1px solid var(--vxe-ui-table-border-color);
      }
    }
  }
}

/*拖拽列*/
.vxe-table-custom-popup--drag-line {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 11;
  pointer-events: none;
}
.vxe-table-custom-popup--drag-line {
  width: 100%;
  height: 1px;
  border: 2px solid transparent;
  &[drag-pos="top"] {
    border-top-color: var(--vxe-ui-font-primary-color);
  }
  &[drag-pos="bottom"] {
    border-bottom-color: var(--vxe-ui-font-primary-color);
  }
  &[drag-to-child="y"] {
    border-top-color: transparent;
    border-bottom-color: transparent;
    border-left-color: var(--vxe-ui-status-success-color);
  }
  &.is--guides {
    background-color: var( --vxe-ui-table-drag-over-background-color);
  }
}

.vxe-table-custom-popup--drag-tip {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  padding: 0.6em 1.4em;
  max-width: 60%;
  min-width: 100px;
  border-radius: var(--vxe-ui-border-radius);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  user-select: none;
  pointer-events: none;
  background-color: var(--vxe-ui-layout-background-color);
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
  z-index: 33;
  &[drag-status="normal"] {
    .vxe-table-custom-popup--drag-tip-normal-status {
      display: block;
    }
  }
  &[drag-status="sub"] {
    .vxe-table-custom-popup--drag-tip-sub-status {
      display: block;
    }
  }
  &[drag-status="group"] {
    .vxe-table-custom-popup--drag-tip-group-status {
      display: block;
    }
  }
  &[drag-status="values"] {
    .vxe-table-custom-popup--drag-tip-values-status {
      display: block;
    }
  }
  &[drag-status="disabled"] {
    .vxe-table-custom-popup--drag-tip-disabled-status {
      display: block;
    }
  }
}
.vxe-table-custom-popup--drag-tip-wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.vxe-table-custom-popup--drag-tip-status {
  padding-right: 0.4em;
}
.vxe-table-custom-popup--drag-tip-disabled-status {
  display: none;
  flex-shrink: 0;
  color: var(--vxe-ui-status-error-color)
}
.vxe-table-custom-popup--drag-tip-normal-status,
.vxe-table-custom-popup--drag-tip-sub-status,
.vxe-table-custom-popup--drag-tip-group-status,
.vxe-table-custom-popup--drag-tip-values-status {
  display: none;
}
.vxe-table-custom-popup--drag-tip-content {
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.vxe-table-custom-popup--name {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.vxe-table-custom-popup--title {
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@for $index from 2 to 10 {
  $paddingLeft: ($index - 1) * 24px;
  .vxe-table-custom-popup--row {
    &.level--#{$index} {
      .vxe-table-custom-popup--name {
        padding-left: $paddingLeft;
      }
    }
  }
}

.vxe-table-custom-popup--column-item {
  position: relative;
  &.col--visible,
  &.col--resizable,
  &.col--fixed {
    text-align: center;
  }
  &.col--resizable {
    & > .vxe-input,
    & > .vxe-number-input {
      width: 100%;
    }
  }
}

.vxe-table-custom-popup--row {
  &.active--drag-origin {
    .vxe-table-custom-popup--column-item {
      opacity: 0.5;
    }
  }
  &.active--drag-target {
    &[drag-pos="top"] {
      .vxe-table-custom-popup--column-item {
        &::after {
          display: block;
          top: -2px;
        }
      }
    }
    &[drag-pos="bottom"] {
      .vxe-table-custom-popup--column-item {
        &::after {
          display: block;
          bottom: -2px;
        }
      }
    }
  }
  &:first-child {
    &[drag-pos="top"] {
      .vxe-table-custom-popup--column-item {
        &::after {
          top: 0;
        }
      }
    }
  }
  &:last-child {
    &[drag-pos="bottom"] {
      .vxe-table-custom-popup--column-item {
        &::after {
          bottom: 0;
        }
      }
    }
  }
}

.vxe-table-custom-popup--column-item {
  &::after {
    display: none;
    content: "";
    position: absolute;
    left: -1px;
    width: calc(100% + 1px);
    height: 2px;
    background-color: var(--vxe-ui-font-primary-color);
    z-index: 12;
  }
  &:last-child {
    &::after {
      width: 100%;
    }
  }
}

.vxe-table-custom--list-move {
  transition-property: transform;
  transition-duration: 0.35s;
  transition-delay: 0.05s;
}

.vxe-table-custom-popup--column-sort-placeholder {
  padding: 0.2em 0.5em;
}
.vxe-table-custom-popup--column-sort-btn {
  font-size: 1.2em;
  padding: 0.2em 0.5em;
  &:not(.is--disabled) {
    cursor: grab;
    &:hover {
      color: var(--vxe-ui-font-primary-color);
    }
    &:active {
      cursor: grabbing;
    }
  }
  &.is--disabled {
    color: var(--vxe-ui-input-disabled-color);
    cursor: not-allowed;
  }
}

.vxe-table-custom-popup--table-sort-help-title,
.vxe-table-custom-popup--table-sort-help-icon {
  vertical-align: middle;
}
.vxe-table-custom-popup--table-sort-help-icon {
  margin-left: 5px;
  cursor: help;
}

.vxe-table-custom-popup--table-col-seq {
  width: 80px;
}
.vxe-table-custom-popup--table-col-sort {
  width: 80px;
}
.vxe-table-custom-popup--table-col-title {
  min-width: 120px;
}
.vxe-table-custom-popup--table-col-width {
  width: 140px;
}
.vxe-table-custom-popup--table-col-fixed {
  width: 200px;
}