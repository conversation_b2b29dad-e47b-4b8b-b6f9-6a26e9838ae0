{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\map\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\map\\index.vue", "mtime": 1740448089611}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdpbmRleCcsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHt9DQogIH0sDQogIGNvbXB1dGVkOiB7fSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmluaXRNYXAoKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgaW5pdE1hcCAoKSB7DQogICAgICB3aW5kb3cuQXJjR2lzVXRpbHMuaW5pdFNjZW5lVmlldyh7IGRpdklkOiAnbWFwSW5zdGFuY2UnIH0pDQogICAgICB0aGlzLmdldFBvaW50KCkNCiAgICB9LA0KICAgIGdldFBvaW50KCkgew0KICAgICAgY29uc3QgdGhhdCA9IHRoaXM7DQogICAgICBjb25zdCBjbG9jayA9IHNldEludGVydmFsKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgaWYgKHdpbmRvdz8udmlldykgew0KICAgICAgICAgIGNsZWFySW50ZXJ2YWwoY2xvY2spOw0KICAgICAgICAgIHdpbmRvdy5BcmNHaXNVdGlscy5tYXBDbGlja0V2ZW50SGFuZGxlLmFkZENvb3JkaW5hdGVMaXN0ZW5lcigocG9pbnQpID0+IHsNCiAgICAgICAgICAgIHRoYXQuJGVtaXQoIm1hcENsaWNrIixwb2ludCkNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSwgMTAwMCk7DQogICAgfQ0KICB9LA0KICB3YXRjaDoge30NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/map", "sourcesContent": ["<template>\r\n    <div class=\"mapContainer\" id=\"mapInstance\" />\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'index',\r\n  data() {\r\n    return {}\r\n  },\r\n  computed: {},\r\n  mounted() {\r\n    this.initMap()\r\n  },\r\n  methods: {\r\n    initMap () {\r\n      window.ArcGisUtils.initSceneView({ divId: 'mapInstance' })\r\n      this.getPoint()\r\n    },\r\n    getPoint() {\r\n      const that = this;\r\n      const clock = setInterval(function () {\r\n        if (window?.view) {\r\n          clearInterval(clock);\r\n          window.ArcGisUtils.mapClickEventHandle.addCoordinateListener((point) => {\r\n            that.$emit(\"mapClick\",point)\r\n          });\r\n        }\r\n      }, 1000);\r\n    }\r\n  },\r\n  watch: {}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n.mapContainer {\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 1;\r\n  background-color: #052c4d;\r\n}\r\n</style>\r\n"]}]}