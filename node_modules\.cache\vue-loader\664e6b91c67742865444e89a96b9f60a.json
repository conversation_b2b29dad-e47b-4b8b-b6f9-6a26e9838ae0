{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\components\\SelectTree\\index.vue?vue&type=style&index=0&id=e8604a26&scoped=true&lang=css", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\components\\SelectTree\\index.vue", "mtime": 1740448089611}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758071060223}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758071061987}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758071060880}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5lbC1zY3JvbGxiYXIgLmVsLXNjcm9sbGJhcl9fdmlldyAuZWwtc2VsZWN0LWRyb3Bkb3duX19pdGVtIHsNCiAgaGVpZ2h0OiBhdXRvOw0KICBtYXgtaGVpZ2h0OiAzMDBweDsNCiAgcGFkZGluZzogMDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgb3ZlcmZsb3cteTogYXV0bzsNCn0NCi5lbC1zZWxlY3QtZHJvcGRvd25fX2l0ZW0uc2VsZWN0ZWQgew0KICBmb250LXdlaWdodDogbm9ybWFsOw0KfQ0KLmVsLXRyZWUgLmVsLXRyZWUtbm9kZV9fY29udGVudCB7DQogIGhlaWdodDogYXV0bzsNCiAgcGFkZGluZzogMCAyMHB4Ow0KfQ0KLmVsLXRyZWUtbm9kZV9fbGFiZWwgew0KICBmb250LXdlaWdodDogbm9ybWFsOw0KfQ0KLmVsLXRyZWUgPj4+IC5pcy1jdXJyZW50IC5lbC10cmVlLW5vZGVfX2xhYmVsIHsNCiAgY29sb3I6ICM0MDlFRkY7DQogIGZvbnQtd2VpZ2h0OiA3MDA7DQp9DQouZWwtdHJlZSA+Pj4gLmlzLWN1cnJlbnQgLmVsLXRyZWUtbm9kZV9fY2hpbGRyZW4gLmVsLXRyZWUtbm9kZV9fbGFiZWwgew0KICBjb2xvcjogIzYwNjI2NjsNCiAgZm9udC13ZWlnaHQ6IG5vcm1hbDsNCn0NCi5lbC1wb3BwZXIgew0KICB6LWluZGV4OiA5OTk5Ow0KfQ0KLmlmaWNhdGlvbl9jb2wgew0KICB3aWR0aDogMjBweDsNCiAgaGVpZ2h0OiAxMHB4Ow0KICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+GA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/SelectTree", "sourcesContent": ["<!-- el-select & el-tree 下拉树形选择 -->\r\n<template>\r\n  <div>\r\n    <el-select ref=\"selectTree\" v-model=\"localValue\" :multiple=\"multiple\" :clearable=\"clearable\" @clear=\"clearHandle\" :disabled=\"disabled\">\r\n      <el-option v-for=\"option in options\" :key=\"option[props.value]\" :value=\"option[props.value]\">\r\n        <el-tree\r\n          :data=\"options\"\r\n          :props=\"props\"\r\n          :node-key=\"props.value\"\r\n          @node-click=\"handleNodeClick\"\r\n          :accordion=\"accordion\"\r\n        >\r\n          <span slot-scope=\"{ data }\">\r\n            <i :class=\"[data.color != null ? 'ification_col' : '']\" :style=\"{'background-color': data.color}\"></i>&nbsp;&nbsp;{{ data.label }}\r\n          </span>\r\n        </el-tree>\r\n      </el-option>\r\n    </el-select>\r\n  </div>\r\n</template>\r\n\r\n<script>export default {\r\n  name: \"el-tree-select\",\r\n  props: {\r\n    // 配置项\r\n    props: {\r\n      type: Object,\r\n      default: () => ({\r\n        value: 'id',\r\n        children: 'children',\r\n        label: 'name'\r\n      })\r\n    },\r\n    // 是否禁用\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 选项列表数据(树形结构的对象数组)\r\n    options: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 初始值（单选或多选）\r\n    value: {\r\n      type: [String, Number, Object, Array], // 允许多种类型\r\n      default: null\r\n    },\r\n    // 可清空选项\r\n    clearable: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    // 自动收起\r\n    accordion: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 是否多选\r\n    multiple: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      localValue: this.value || (this.multiple ? [] : null), // 使用 value 或者默认值\r\n    }\r\n  },\r\n  watch: {\r\n    value: {\r\n      immediate: true,\r\n      handler(newValue) {\r\n        this.localValue = newValue;\r\n      }\r\n    },\r\n    localValue: {\r\n      handler(newValue) {\r\n        this.$emit('input', newValue);\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    // 清除选中\r\n    clearHandle() {\r\n      this.localValue = this.multiple ? [] : null;\r\n      this.clearSelected();\r\n      this.$emit('input', this.localValue);\r\n    },\r\n    // 切换选项\r\n    handleNodeClick(node) {\r\n      if (this.multiple) {\r\n        // 多选（判重后添加）\r\n        if (!this.localValue.includes(node[this.props.label])) {\r\n          this.localValue.push(node[this.props.label]);\r\n        }\r\n      } else {\r\n        // 单选\r\n        this.localValue = node[this.props.label];\r\n      }\r\n      this.$emit('input', this.localValue);\r\n    },\r\n    // 清空选中样式\r\n    clearSelected() {\r\n      const treeNodes = this.$refs.selectTree.$el.querySelectorAll('.el-tree-node');\r\n      treeNodes.forEach(node => node.classList.remove('is-current'));\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style scoped>\r\n.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {\r\n  height: auto;\r\n  max-height: 300px;\r\n  padding: 0;\r\n  overflow: hidden;\r\n  overflow-y: auto;\r\n}\r\n.el-select-dropdown__item.selected {\r\n  font-weight: normal;\r\n}\r\n.el-tree .el-tree-node__content {\r\n  height: auto;\r\n  padding: 0 20px;\r\n}\r\n.el-tree-node__label {\r\n  font-weight: normal;\r\n}\r\n.el-tree >>> .is-current .el-tree-node__label {\r\n  color: #409EFF;\r\n  font-weight: 700;\r\n}\r\n.el-tree >>> .is-current .el-tree-node__children .el-tree-node__label {\r\n  color: #606266;\r\n  font-weight: normal;\r\n}\r\n.el-popper {\r\n  z-index: 9999;\r\n}\r\n.ification_col {\r\n  width: 20px;\r\n  height: 10px;\r\n  display: inline-block;\r\n}\r\n</style>\r\n"]}]}