{"remainingRequest": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project_about\\git174\\alznt-admin\\src\\views\\dashboard\\RaddarChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project_about\\git174\\alznt-admin\\src\\views\\dashboard\\RaddarChart.vue", "mtime": 1718070340324}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758071060885}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758071059602}, {"path": "D:\\project_about\\git174\\alznt-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758071061376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["RaddarChart.vue"], "names": [], "mappings": ";;;;;AAKA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RaddarChart.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\r\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\r\n</template>\r\n\r\n<script>\r\nimport echarts from 'echarts'\r\nrequire('echarts/theme/macarons') // echarts theme\r\nimport resize from './mixins/resize'\r\n\r\nconst animationDuration = 3000\r\n\r\nexport default {\r\n  mixins: [resize],\r\n  props: {\r\n    className: {\r\n      type: String,\r\n      default: 'chart'\r\n    },\r\n    width: {\r\n      type: String,\r\n      default: '100%'\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '300px'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chart: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initChart()\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    if (!this.chart) {\r\n      return\r\n    }\r\n    this.chart.dispose()\r\n    this.chart = null\r\n  },\r\n  methods: {\r\n    initChart() {\r\n      this.chart = echarts.init(this.$el, 'macarons')\r\n\r\n      this.chart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { // 坐标轴指示器，坐标轴触发有效\r\n            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'\r\n          }\r\n        },\r\n        radar: {\r\n          radius: '66%',\r\n          center: ['50%', '42%'],\r\n          splitNumber: 8,\r\n          splitArea: {\r\n            areaStyle: {\r\n              color: 'rgba(127,95,132,.3)',\r\n              opacity: 1,\r\n              shadowBlur: 45,\r\n              shadowColor: 'rgba(0,0,0,.5)',\r\n              shadowOffsetX: 0,\r\n              shadowOffsetY: 15\r\n            }\r\n          },\r\n          indicator: [\r\n            { name: 'Sales', max: 10000 },\r\n            { name: 'Administration', max: 20000 },\r\n            { name: 'Information Techology', max: 20000 },\r\n            { name: 'Customer Support', max: 20000 },\r\n            { name: 'Development', max: 20000 },\r\n            { name: 'Marketing', max: 20000 }\r\n          ]\r\n        },\r\n        legend: {\r\n          left: 'center',\r\n          bottom: '10',\r\n          data: ['Allocated Budget', 'Expected Spending', 'Actual Spending']\r\n        },\r\n        series: [{\r\n          type: 'radar',\r\n          symbolSize: 0,\r\n          areaStyle: {\r\n            normal: {\r\n              shadowBlur: 13,\r\n              shadowColor: 'rgba(0,0,0,.2)',\r\n              shadowOffsetX: 0,\r\n              shadowOffsetY: 10,\r\n              opacity: 1\r\n            }\r\n          },\r\n          data: [\r\n            {\r\n              value: [5000, 7000, 12000, 11000, 15000, 14000],\r\n              name: 'Allocated Budget'\r\n            },\r\n            {\r\n              value: [4000, 9000, 15000, 15000, 13000, 11000],\r\n              name: 'Expected Spending'\r\n            },\r\n            {\r\n              value: [5500, 11000, 12000, 15000, 12000, 12000],\r\n              name: 'Actual Spending'\r\n            }\r\n          ],\r\n          animationDuration: animationDuration\r\n        }]\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}